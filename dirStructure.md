# X10sion Project Directory Structure

*Last Updated: May 25, 2025*

This document outlines the directory structure of the X10sion project, a local-first AI coding co-pilot for VS Code. This structure follows May 2025 best practices for modular TypeScript development and AI agent frameworks.

## Root Directory

```
x10sion/
├── src/                           # Source code for the extension
│   ├── extension.ts               # Main extension code
│   ├── test/                      # Test files
│   │   ├── extension.test.ts      # Extension tests
│   │   └── suite/                 # Test suite
│   │       ├── shell-command-tool.test.ts # Shell command tool tests
│   │       └── file-system-tool.test.ts # File system tool tests
│   ├── ui/                        # UI components (for Phase 3+)
│   │   ├── human-in-the-loop-view.ts # Human-in-the-loop view (moved to human-in-the-loop folder)
│   │   └── webview/               # Webview panel implementation
│   ├── llm/                       # LLM interaction code
│   │   ├── ollama.ts              # Ollama API integration
│   │   ├── prompt.ts              # Prompt templates and enhancement
│   │   └── providers/             # LLM providers for different models
│   │       ├── base-provider.ts   # Base LLM provider interface
│   │       ├── ollama-provider.ts # Ollama-specific provider
│   │       ├── openai-provider.ts # OpenAI provider (optional)
│   │       └── anthropic-provider.ts # Anthropic provider (optional)
│   ├── context/                   # Context gathering code
│   │   ├── editor.ts              # Editor context extraction
│   │   └── guidelines.ts          # Guidelines processing
│   ├── rag/                       # RAG implementation (Phase 4+)
│   │   ├── indexing.ts            # Document indexing
│   │   ├── embedding.ts           # Embedding generation
│   │   └── retrieval.ts           # Similarity search and retrieval
│   ├── mcp/                       # Model Context Protocol implementation
│   │   ├── server.ts              # MCP server implementation
│   │   ├── client.ts              # MCP client implementation
│   │   ├── transport.ts           # MCP transport layer
│   │   ├── registry.ts            # MCP server registry
│   │   ├── types.ts               # MCP type definitions
│   │   ├── interfaces.ts          # MCP interface definitions
│   │   ├── utils.ts               # MCP utility functions
│   │   ├── resources/             # MCP resources
│   │   │   ├── editor-context.ts  # Editor context resource
│   │   │   ├── file-content.ts    # File content resource
│   │   │   ├── guidelines.ts      # Guidelines resource
│   │   │   └── rag-knowledge-base.ts # RAG knowledge base resource
│   │   ├── tools/                 # MCP tools
│   │   │   ├── shell-command.ts   # Shell command tool
│   │   │   ├── file-system.ts     # File system tool (217 lines - ✅ refactored)
│   │   │   ├── file-system/       # Modular file system components (May 2025 refactoring)
│   │   │   │   ├── types.ts       # Type definitions and interfaces
│   │   │   │   ├── utils.ts       # Utility functions and path resolution
│   │   │   │   ├── read-operations.ts  # File reading operations
│   │   │   │   ├── write-operations.ts # File writing operations
│   │   │   │   ├── file-management.ts  # File management (copy, move, delete)
│   │   │   │   └── directory-operations.ts # Directory operations
│   │   │   ├── workspace-search.ts # Workspace search tool
│   │   │   └── rag-indexing.ts    # RAG indexing tool
│   │   └── prompts/               # MCP prompts
│   │       ├── code-review.ts     # Code review prompt
│   │       ├── documentation-generation.ts # Documentation generation prompt
│   │       └── common-prompts.ts  # Common prompts
│   ├── human-in-the-loop/         # Human-in-the-loop functionality
│   │   └── agent.ts               # Human-in-the-loop agent
│   ├── agents/                    # AI agents implementation
│   │   ├── framework/             # Agent framework
│   │   │   ├── agent-system.ts    # Core agent system
│   │   │   ├── agent-factory.ts   # Agent factory
│   │   │   ├── agent-registry.ts  # Agent registry
│   │   │   ├── agent-orchestrator.ts # Agent orchestrator (main)
│   │   │   ├── orchestrator/      # Orchestrator modules
│   │   │   │   ├── types.ts       # Type definitions
│   │   │   │   ├── workflow-executor.ts # Workflow execution logic
│   │   │   │   ├── step-executor.ts # Step execution logic
│   │   │   │   └── dependency-resolver.ts # Dependency resolution
│   │   │   └── agent-memory-manager.ts # Agent memory manager
│   │   ├── base-agent.ts          # Base agent class (317 lines - ✅ refactored)
│   │   ├── base/                  # Base agent modular components (May 2025 refactoring)
│   │   │   ├── types.ts           # Type definitions and interfaces
│   │   │   ├── memory-manager.ts  # Memory management
│   │   │   ├── task-manager.ts    # Task management and queuing
│   │   │   ├── llm-integration.ts # LLM communication and monitoring
│   │   │   └── collaboration-manager.ts # Agent collaboration
│   │   ├── code-generation-agent.ts # Code generation agent (main)
│   │   ├── code-generation/       # Code generation modules
│   │   │   ├── language-detector.ts # Language detection utilities
│   │   │   ├── code-parser.ts     # Code parsing utilities
│   │   │   └── prompt-builder.ts  # Prompt building utilities
│   │   ├── prompt-enhancement-agent.ts # Prompt enhancement agent (main)
│   │   ├── prompt-enhancement/    # Prompt enhancement modules
│   │   │   ├── prompt-analyzer.ts # Prompt analysis utilities
│   │   │   ├── context-prioritizer.ts # Context prioritization
│   │   │   └── token-manager.ts   # Token management utilities
│   │   ├── human-in-the-loop-config.ts # Human-in-the-loop configuration
│   │   ├── code-reviewer-agent.ts # Code review agent
│   │   ├── documentation-agent.ts # Documentation agent
│   │   ├── debug-assistant-agent.ts # Debugging agent
│   │   ├── test-generator-agent.ts # Test generation agent
│   │   ├── prompt-enhancement-agent.ts # Prompt enhancement agent
│   │   ├── dependency-management-agent.ts # Dependency management agent
│   │   ├── ux-improvement-agent.ts # UX improvement agent
│   │   ├── integration-agent.ts    # Integration agent
│   │   ├── feedback-collection-agent.ts # Feedback collection agent
│   │   ├── system-optimization-agent.ts # System optimization agent
│   │   ├── telemetry-agent.ts      # Telemetry agent
│   │   ├── feedback-analysis-agent.ts # Feedback analysis agent
│   │   ├── upgrade-agent.ts        # Upgrade agent
│   │   └── model-tuning-agent.ts   # Model fine-tuning agent
│   ├── parallel/                  # Parallel processing
│   │   ├── worker-pool.ts         # Worker thread pool
│   │   ├── worker.js              # Worker implementation
│   │   ├── task-scheduler.ts      # Task scheduler
│   │   ├── resource-monitor.ts    # Resource monitor
│   │   └── background-worker-manager.ts # Background worker manager
│   ├── monitoring/                # Monitoring systems
│   │   ├── llm-monitor.ts         # LLM monitor re-export (for backward compatibility)
│   │   ├── pattern-matcher.ts     # Pattern matching service for terminal monitoring
│   │   ├── buffer-manager.ts      # Buffer management service for terminal monitoring
│   │   ├── terminal-monitor.ts    # Terminal output monitor (main)
│   │   ├── terminal-monitor/      # Terminal monitor modules
│   │   │   ├── pattern-registry.ts # Pattern management
│   │   │   ├── terminal-tracker.ts # Terminal tracking
│   │   │   └── output-processor.ts # Output processing
│   │   └── llm-monitor/           # LLM monitoring system (modular)
│   │       ├── index.ts           # Main LLM monitor class (402 lines - ✅ refactored)
│   │       ├── types.ts           # Type definitions
│   │       ├── data-manager.ts    # Data storage and management (May 2025 refactoring)
│   │       ├── tracking-service.ts # Request/response tracking (May 2025 refactoring)
│   │       ├── status-manager.ts  # Status bar management (May 2025 refactoring)
│   │       ├── event-handler.ts   # Event handling service
│   │       ├── agent-manager.ts   # Agent management service
│   │       ├── metrics.ts         # Main metrics service
│   │       ├── time-stats.ts      # Time-based statistics service
│   │       ├── agent-metrics.ts   # Agent-specific metrics service
│   │       ├── issue-detection.ts # Issue detection service
│   │       ├── interventions.ts   # Intervention service
│   │       └── persistence.ts     # Data persistence service
│   ├── file-management/           # File management
│   │   ├── file-registry.ts       # File registry
│   │   └── content-manager.ts     # Content manager
│   ├── workers/                   # Worker implementations
│   │   └── documentation-worker.js # Documentation worker
│   └── optimization/              # Optimization techniques
│       ├── lazy-loader.ts         # Lazy loading
│       ├── memory-optimizer.ts    # Memory optimization
│       └── incremental-processor.ts # Incremental processing
├── out/                           # Compiled JavaScript files
├── docs/                          # Documentation
│   ├── user_guide/                # User documentation
│   │   ├── getting_started.md     # Getting started guide
│   │   ├── features.md            # Features documentation
│   │   └── configuration.md       # Configuration options
│   ├── features/                  # Feature documentation
│   │   └── human-in-the-loop.md   # Human-in-the-loop documentation
│   └── dev_guide/                 # Developer documentation
│       ├── devPhase0/             # Phase 0 documentation
│       │   ├── devPhase0.md       # Phase 0 overview
│       │   └── devPhase0_tasks.md # Phase 0 tasks
│       ├── devPhase1/             # Phase 1 documentation
│       │   ├── devPhase1.md       # Phase 1 overview
│       │   └── devPhase1_tasks.md # Phase 1 tasks
│       ├── devPhase2/             # Phase 2 documentation
│       │   ├── devPhase2.md       # Phase 2 overview
│       │   └── devPhase2_tasks.md # Phase 2 tasks
│       ├── devPhase3/             # Phase 3 documentation
│       │   ├── devPhase3.md       # Phase 3 overview
│       │   └── devPhase3_tasks.md # Phase 3 tasks
│       ├── devPhase4/             # Phase 4 documentation
│       │   ├── devPhase4.md       # Phase 4 overview
│       │   └── devPhase4_tasks.md # Phase 4 tasks
│       ├── devPhase5/             # Phase 5 documentation
│       │   ├── devPhase5.md       # Phase 5 overview
│       │   └── devPhase5_tasks.md # Phase 5 tasks
│       ├── devPhase6/             # Phase 6 documentation
│       │   ├── devPhase6.md       # Phase 6 overview
│       │   └── devPhase6_tasks.md # Phase 6 tasks
│       ├── devPhase7/             # Phase 7 documentation
│       │   ├── devPhase7.md       # Phase 7 overview
│       │   └── devPhase7_tasks.md # Phase 7 tasks
│       ├── devPhase8/             # Phase 8 documentation
│       │   ├── devPhase8.md       # Phase 8 overview
│       │   └── devPhase8_tasks.md # Phase 8 tasks
│       ├── devPhase9/             # Phase 9 documentation
│       │   ├── devPhase9.md       # Phase 9 overview
│       │   └── devPhase9_tasks.md # Phase 9 tasks
│       ├── devPhase10/            # Phase 10 documentation
│       │   ├── devPhase10.md      # Phase 10 overview
│       │   └── devPhase10_tasks.md # Phase 10 tasks
│       ├── devPhase11/            # Phase 11 documentation
│       │   ├── devPhase11.md      # Phase 11 overview
│       │   └── devPhase11_tasks.md # Phase 11 tasks
│       ├── devPhase12/            # Phase 12 documentation
│       │   ├── devPhase12.md      # Phase 12 overview
│       │   └── devPhase12_tasks.md # Phase 12 tasks
│       ├── devPhase13/            # Phase 13 documentation
│       │   ├── devPhase13.md      # Phase 13 overview
│       │   └── devPhase13_tasks.md # Phase 13 tasks
│       ├── devPhase14/            # Phase 14 documentation
│       │   ├── devPhase14.md      # Phase 14 overview
│       │   └── devPhase14_tasks.md # Phase 14 tasks
│       ├── devPhase15/            # Phase 15 documentation
│       │   ├── devPhase15.md      # Phase 15 overview
│       │   └── devPhase15_tasks.md # Phase 15 tasks
│       ├── devPhase16/            # Phase 16 documentation
│       │   ├── devPhase16.md      # Phase 16 overview
│       │   └── devPhase16_tasks.md # Phase 16 tasks
│       ├── devPhase17/            # Phase 17 documentation
│       │   ├── devPhase17.md      # Phase 17 overview
│       │   └── devPhase17_tasks.md # Phase 17 tasks
│       └── development_phases.md  # Overview of all development phases
├── knowledge_base/                # Knowledge base for RAG
│   ├── code_patterns/             # Common code patterns
│   │   ├── typescript_patterns.md # TypeScript patterns
│   │   └── javascript_patterns.md # JavaScript patterns
│   ├── best_practices/            # Best practices
│   │   ├── vscode_extension_best_practices.md # VS Code extension best practices
│   │   ├── vscode_native_ui_best_practices.md # VS Code native UI best practices
│   │   ├── typescript_best_practices.md       # TypeScript best practices
│   │   ├── dependency_management.md           # Dependency management best practices
│   │   ├── ollama_integration_best_practices.md # Ollama integration best practices
│   │   ├── rag_implementation_best_practices.md # RAG implementation best practices
│   │   ├── mcp_implementation_best_practices.md # MCP implementation best practices
│   │   └── ai_agent_marketplace_ui_ux.md      # AI agent marketplace UI/UX best practices
│   ├── examples/                  # Example files for testing and documentation
│   │   ├── example_queries.md     # Example queries for testing
│   │   └── example_responses.md   # Example expected responses
│   ├── security/                  # Security best practices
│   │   ├── ollama_security_best_practices.md  # Ollama security best practices
│   │   └── ai_agent_security_best_practices.md # AI agent security best practices
│   └── architecture/              # Architecture documentation
│       ├── ai_agent_implementation.md         # AI agent implementation details
│       ├── ai_agents.md                       # AI agent architecture overview
│       ├── agi_integration.md                 # AGI integration strategies
│       ├── background_worker_system.md        # Background worker system
│       ├── mcp_components.md                  # MCP components details
│       ├── optimization_techniques.md         # Optimization techniques
│       └── webassembly_integration.md         # WebAssembly integration

├── testResults/                   # Test results
│   ├── phase0/                    # Phase 0 test results
│   │   └── phase0_full_testResult.md   # Full phase 0 test results
│   ├── phase1/                    # Phase 1 test results
│   │   └── phase1_full_testResult.md   # Full phase 1 test results
│   ├── phase2/                    # Phase 2 test results
│   │   └── phase2_full_testResult.md   # Full phase 2 test results
│   ├── phase3/                    # Phase 3 test results
│   │   └── phase3_full_testResult.md   # Full phase 3 test results
│   ├── phase4/                    # Phase 4 test results
│   │   └── phase4_full_testResult.md   # Full phase 4 test results
│   ├── phase5/                    # Phase 5 test results
│   │   └── phase5_full_testResult.md   # Full phase 5 test results
│   ├── phase6/                    # Phase 6 test results
│   │   └── phase6_full_testResult.md   # Full phase 6 test results
│   ├── phase7/                    # Phase 7 test results
│   │   ├── phase7_task6_testResult.md  # Shell command tool test results
│   │   ├── phase7_task7_testResult.md  # File system tool test results
│   │   ├── phase7_task8_testResult.md  # Human-in-the-loop agent test results
│   │   └── phase7_full_testResult.md   # Full phase 7 test results
│   ├── phase8/                    # Phase 8 test results
│   │   └── phase8_full_testResult.md   # Full phase 8 test results
│   ├── phase9/                    # Phase 9 test results
│   │   └── phase9_full_testResult.md   # Full phase 9 test results
│   ├── phase10/                   # Phase 10 test results
│   │   └── phase10_full_testResult.md  # Full phase 10 test results
│   ├── phase11/                   # Phase 11 test results
│   │   └── phase11_full_testResult.md  # Full phase 11 test results
│   ├── phase12/                   # Phase 12 test results
│   │   └── phase12_full_testResult.md  # Full phase 12 test results
│   ├── phase13/                   # Phase 13 test results
│   │   └── phase13_full_testResult.md  # Full phase 13 test results
│   ├── phase14/                   # Phase 14 test results
│   │   └── phase14_full_testResult.md  # Full phase 14 test results
│   ├── phase15/                   # Phase 15 test results
│   │   └── phase15_full_testResult.md  # Full phase 15 test results
│   ├── phase16/                   # Phase 16 test results
│   │   └── phase16_full_testResult.md  # Full phase 16 test results
│   └── phase17/                   # Phase 17 test results
│       └── phase17_full_testResult.md  # Full phase 17 test results
├── .vscode/                       # VS Code configuration
│   ├── launch.json                # Launch configuration
│   ├── tasks.json                 # Task configuration
│   ├── settings.json              # Settings
│   └── extensions.json            # Recommended extensions
├── node_modules/                  # Node.js dependencies (not tracked in git)
├── package.json                   # Project manifest
├── package-lock.json              # Dependency lock file
├── tsconfig.json                  # TypeScript configuration
├── eslint.config.mjs              # ESLint configuration
├── .gitignore                     # Git ignore file
├── .vscodeignore                  # VS Code ignore file
├── .vscode-test.mjs               # VS Code test configuration
├── CHANGELOG.md                   # Change log
├── README.md                      # Project overview
├── README.md.bak                  # README backup for comparison
├── fileRelations.md               # File relationships documentation
├── dirStructure.md                # This file - directory structure
├── testMethod.md                  # Testing methodology documentation
├── vsc-extension-quickstart.md    # VS Code extension quickstart guide
├── x10sion_general_guidelines.md  # General AI assistant guidelines
└── x10sion_project_guidelines.md  # Project-specific guidelines
```

## Directory Structure Explanation

### Source Code (`src/`)
Contains all the TypeScript source code for the extension, organized by feature area.

### Documentation (`docs/`)
Contains user and developer documentation, with developer documentation organized by development phases.

### Knowledge Base (`knowledge_base/`)
Contains markdown files that serve as the knowledge base for the RAG system, organized by topic.

### Examples (`examples/`)
Contains example queries and expected responses for testing the extension.

### Test Results (`testResults/`)
Contains test results organized by development phase.

### Configuration Files
Various configuration files for TypeScript, ESLint, VS Code, etc.

### Guidelines Files
Root-level markdown files that provide guidelines for the AI assistant.
