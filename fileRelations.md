# X10sion File Relationships

*Last Updated: May 26, 2025*

This document outlines the relationships between files in the X10sion project, showing how different components interact with each other. This follows May 2025 modular architecture patterns and includes all 17 development phases.

## 🎉 **REFACTORING MILESTONE ACHIEVED**
- **✅ 100% Test Pass Rate**: 119/119 tests passing
- **✅ Complete Refactoring**: All 5 oversized files successfully refactored to <500 lines
- **✅ 23 Modular Components**: Created from refactoring initiative
- **✅ Zero Compilation Errors**: All TypeScript issues resolved

## Core Extension Files

### `src/extension.ts`
- **Main entry point** for the extension
- **Imports**:
  - `vscode` - VS Code API
  - `node-fetch` - For HTTP requests to Ollama
  - `src/context/editor.ts` - For editor context gathering
  - `src/llm/ollama.ts` - For Ollama API interaction
  - `src/llm/prompt.ts` - For prompt enhancement (Phase 2.3+)
  - `src/context/guidelines.ts` - For guidelines processing (Phase 3.3+)
  - `src/ui/webview/panel.ts` - For webview panel (Phase 3.1+)
  - `src/ui/human-in-the-loop-view.js` - For human-in-the-loop UI
  - `src/human-in-the-loop/agent.js` - For human-in-the-loop functionality
  - `src/agents/human-in-the-loop-config.js` - For human-in-the-loop configuration
  - `src/rag/retrieval.ts` - For RAG functionality (Phase 4+)
  - `src/parallel/worker-pool.ts` - For parallel processing (Phase 6+)
  - `src/parallel/task-scheduler.ts` - For task scheduling (Phase 6+)
  - `src/parallel/resource-monitor.ts` - For resource monitoring (Phase 6+)
  - `src/mcp/types.ts` - For MCP type definitions (Phase 6+)
  - `src/mcp/interfaces.ts` - For MCP interface definitions (Phase 6+)
  - `src/mcp/utils.ts` - For MCP utility functions (Phase 6+)
  - `src/mcp/server.ts` - For MCP server (Phase 7+)
  - `src/mcp/client.ts` - For MCP client (Phase 7+)
  - `src/agents/framework/agent-system.ts` - For agent system (Phase 8+)
  - `src/monitoring/llm-monitor.ts` - For LLM monitoring (Phase 10+)
  - `src/monitoring/terminal-monitor.ts` - For terminal monitoring (Phase 10+)
  - `src/file-management/file-registry.ts` - For file management (Phase 10+)
  - `src/parallel/background-worker-manager.ts` - For background workers (Phase 11+)
  - `src/optimization/lazy-loader.ts` - For lazy loading (Phase 11+)
  - `src/agents/ux-improvement-agent.ts` - For UX improvement agent (Phase 12+)
  - `src/agents/integration-agent.ts` - For integration agent (Phase 12+)
  - `src/agents/feedback-collection-agent.ts` - For feedback collection agent (Phase 12+)
  - `src/agents/system-optimization-agent.ts` - For system optimization agent (Phase 12+)
  - `src/agents/telemetry-agent.ts` - For telemetry agent (Phase 13+)
  - `src/agents/feedback-analysis-agent.ts` - For feedback analysis agent (Phase 13+)
  - `src/agents/upgrade-agent.ts` - For upgrade agent (Phase 13+)
  - `src/agents/model-tuning-agent.ts` - For model fine-tuning agent (Phase 13+)
  - `src/configuration/configuration-service.ts` - For configuration management (Phase 14+)
  - `src/ui/admin/admin-panel.ts` - For admin interface (Phase 14+)
  - `src/ui/settings/workspace-settings.ts` - For user settings (Phase 14+)
  - `src/agents/user-intent-analysis-agent.ts` - For user intent analysis (Phase 15+)
  - `src/agents/hardware-analysis-agent.ts` - For hardware analysis (Phase 15+)
  - `src/agents/llm-recommendation-agent.ts` - For LLM recommendations (Phase 15+)
  - `src/agents/security-compliance-agent.ts` - For security and compliance (Phase 16+)
  - `src/agents/architecture-design-agent.ts` - For architecture design (Phase 16+)
  - `src/agents/information-gathering-agent.ts` - For ethical information gathering (Phase 17+)
- **Exports**:
  - `activate` - Function called when extension is activated
  - `deactivate` - Function called when extension is deactivated

### `package.json`
- **Defines**:
  - Extension metadata
  - Command contributions
  - Dependencies
  - Build scripts
- **Referenced by**:
  - VS Code for extension registration
  - npm for dependency management

## Context Gathering

### `src/context/editor.ts`
- **Purpose**: Gather context from the active editor
- **Exports**:
  - `getActiveEditorContext` - Function to get context from active editor
- **Used by**:
  - `src/extension.ts` - To get editor context for LLM queries

### `src/context/guidelines.ts` (Phase 3.3+)
- **Purpose**: Read and process guideline files
- **Imports**:
  - `vscode` - For file system access
- **Exports**:
  - `readGuidelines` - Function to read guideline files
- **Used by**:
  - `src/extension.ts` - To include guidelines in context
- **References**:
  - `x10sion_general_guidelines.md` - General guidelines file
  - `x10sion_project_guidelines.md` - Project-specific guidelines file

## LLM Interaction

### `src/llm/ollama.ts`
- **Purpose**: Interact with Ollama API
- **Imports**:
  - `node-fetch` - For HTTP requests
  - `vscode` - For showing messages
- **Exports**:
  - `sendToOllama` - Function to send prompts to Ollama
- **Used by**:
  - `src/extension.ts` - To send queries to Ollama

### `src/llm/prompt.ts` (Phase 2.3+)
- **Purpose**: Enhance prompts with context
- **Exports**:
  - `enhancePrompt` - Function to enhance prompts with context
- **Used by**:
  - `src/extension.ts` - To prepare prompts for Ollama

## UI Components (Phase 3+)

### `src/ui/webview/panel.ts`
- **Purpose**: Implement webview panel for chat
- **Imports**:
  - `vscode` - For webview API
- **Exports**:
  - `ChatPanel` - Class for chat panel
- **Used by**:
  - `src/extension.ts` - To create and manage chat panel

### `src/human-in-the-loop/view.ts` (Future implementation)
- **Purpose**: VS Code TreeView for human-in-the-loop interactions
- **Imports**:
  - `vscode` - VS Code API
  - `src/human-in-the-loop/agent.ts` - For agent interfaces and enums
- **Exports**:
  - `registerHumanInTheLoopView` function - Register the view
  - `HumanInTheLoopDataProvider` class - Data provider for the view
- **Used by**:
  - `src/extension.ts` - For initializing the view

## RAG Implementation (Phase 4+)

### `src/rag/indexing.ts`
- **Purpose**: Index markdown files for RAG
- **Imports**:
  - `vscode` - For file system access
- **Exports**:
  - `indexMarkdownFiles` - Function to index markdown files
- **Used by**:
  - `src/extension.ts` - To build RAG index
- **References**:
  - Markdown files in `knowledge_base/` and elsewhere

### `src/rag/embedding.ts`
- **Purpose**: Generate embeddings for text
- **Imports**:
  - `node-fetch` - For Ollama embedding API
- **Exports**:
  - `generateEmbedding` - Function to generate embeddings
- **Used by**:
  - `src/rag/indexing.ts` - To embed indexed content
  - `src/rag/retrieval.ts` - To embed queries

### `src/rag/retrieval.ts`
- **Purpose**: Retrieve relevant content based on queries
- **Imports**:
  - `src/rag/embedding.ts` - For embedding generation
- **Exports**:
  - `retrieveRelevantContent` - Function to retrieve relevant content
- **Used by**:
  - `src/extension.ts` - To enhance context with RAG results
  - `src/mcp/server.ts` - As a resource for the MCP server

## Model Context Protocol (MCP)

### `src/mcp/types.ts` (Phase 6+)
- **Purpose**: Define type definitions for MCP components
- **Imports**:
  - `@modelcontextprotocol/sdk/types.js` - MCP SDK types
- **Exports**:
  - Type definitions for resources, tools, prompts, and agents
- **Used by**:
  - All MCP-related files

### `src/mcp/interfaces.ts` (Phase 6+)
- **Purpose**: Define interface definitions for MCP components
- **Imports**:
  - `src/mcp/types.ts` - MCP type definitions
- **Exports**:
  - Interface definitions for MCP server, client, and handlers
- **Used by**:
  - All MCP-related files

### `src/mcp/utils.ts` (Phase 6+)
- **Purpose**: Provide utility functions for MCP components
- **Imports**:
  - `src/mcp/types.ts` - MCP type definitions
  - `src/mcp/interfaces.ts` - MCP interface definitions
- **Exports**:
  - Utility functions for MCP components
- **Used by**:
  - All MCP-related files

### `src/mcp/server.ts` (Phase 7+)
- **Purpose**: Implement MCP server for the extension
- **Imports**:
  - `@modelcontextprotocol/sdk/server/mcp.js` - MCP server SDK
  - `src/mcp/types.ts` - MCP type definitions
  - `src/mcp/interfaces.ts` - MCP interface definitions
  - `src/mcp/utils.ts` - MCP utility functions
  - `src/context/editor.ts` - For editor context
  - `src/rag/system.ts` - For RAG functionality
  - `src/parallel/worker-pool.ts` - For parallel processing
- **Exports**:
  - `X10sionMcpServer` - MCP server class
- **Used by**:
  - `src/extension.ts` - To create and manage the MCP server

### `src/mcp/resources/editor-context.ts` (Phase 7+)
- **Purpose**: Implement editor context resource
- **Imports**:
  - `vscode` - VS Code API
  - `src/context/editor.ts` - For editor context gathering
- **Exports**:
  - Editor context resource handler
- **Used by**:
  - `src/mcp/server.ts` - To register the resource

### `src/mcp/resources/file-content.ts` (Phase 7+)
- **Purpose**: Implement file content resource
- **Imports**:
  - `vscode` - VS Code API
- **Exports**:
  - File content resource handler
- **Used by**:
  - `src/mcp/server.ts` - To register the resource

### `src/mcp/resources/guidelines.ts` (Phase 7+)
- **Purpose**: Implement guidelines resource
- **Imports**:
  - `src/context/guidelines.ts` - For guidelines processing
- **Exports**:
  - Guidelines resource handler
- **Used by**:
  - `src/mcp/server.ts` - To register the resource

### `src/mcp/resources/rag-knowledge-base.ts` (Phase 7+)
- **Purpose**: Implement RAG knowledge base resource
- **Imports**:
  - `src/rag/retrieval.ts` - For RAG functionality
- **Exports**:
  - RAG knowledge base resource handler
- **Used by**:
  - `src/mcp/server.ts` - To register the resource

### `src/mcp/tools/shell-command.ts` (Phase 7+)
- **Purpose**: Implement shell command tool
- **Imports**:
  - `vscode` - VS Code API
  - `child_process` - Node.js child process
  - `os` - Node.js OS utilities
  - `src/mcp/types.js` - MCP type definitions
  - `src/mcp/utils.js` - MCP utility functions
  - `src/human-in-the-loop/agent.js` - Human-in-the-loop agent
- **Exports**:
  - `shellCommandTool` - Shell command tool implementation
  - `registerShellCommandTool` - Function to register the tool
- **Used by**:
  - `src/mcp/server.ts` - To register the tool
  - `src/extension.ts` - To initialize the tool

### `src/mcp/tools/file-system.ts` (Phase 7+)
- **Purpose**: Implement file system tool
- **Imports**:
  - `vscode` - VS Code API
  - `fs` - Node.js file system
  - `path` - Node.js path
  - `src/mcp/types.js` - MCP type definitions
  - `src/mcp/utils.js` - MCP utility functions
  - `src/human-in-the-loop/agent.js` - Human-in-the-loop agent
- **Exports**:
  - `fileSystemTool` - File system tool implementation
  - `FileSystemOperationType` - Enum for file system operations
  - `registerFileSystemTool` - Function to register the tool
- **Used by**:
  - `src/mcp/server.ts` - To register the tool
  - `src/extension.ts` - To initialize the tool

### `src/mcp/tools/workspace-search.ts` (Phase 7+)
- **Purpose**: Implement workspace search tool
- **Imports**:
  - `vscode` - VS Code API
- **Exports**:
  - Workspace search tool handler
- **Used by**:
  - `src/mcp/server.ts` - To register the tool

### `src/mcp/tools/rag-indexing.ts` (Phase 7+)
- **Purpose**: Implement RAG indexing tool
- **Imports**:
  - `src/rag/indexing.ts` - For RAG indexing
- **Exports**:
  - RAG indexing tool handler
- **Used by**:
  - `src/mcp/server.ts` - To register the tool

### `src/mcp/prompts/code-review.ts` (Phase 7+)
- **Purpose**: Implement code review prompt
- **Exports**:
  - Code review prompt handler
- **Used by**:
  - `src/mcp/server.ts` - To register the prompt

### `src/mcp/prompts/documentation-generation.ts` (Phase 7+)
- **Purpose**: Implement documentation generation prompt
- **Exports**:
  - Documentation generation prompt handler
- **Used by**:
  - `src/mcp/server.ts` - To register the prompt

### `src/mcp/prompts/common-prompts.ts` (Phase 7+)
- **Purpose**: Implement common prompts
- **Exports**:
  - Common prompt handlers
- **Used by**:
  - `src/mcp/server.ts` - To register the prompts

### `src/mcp/client.ts` (Phase 7+)
- **Purpose**: Implement MCP client for the extension
- **Imports**:
  - `@modelcontextprotocol/sdk/client/index.js` - MCP client SDK
  - `@modelcontextprotocol/sdk/client/streamableHttp.js` - Streamable HTTP transport
  - `src/mcp/types.ts` - MCP type definitions
  - `src/mcp/interfaces.ts` - MCP interface definitions
  - `src/mcp/utils.ts` - MCP utility functions
- **Exports**:
  - `X10sionMcpClient` - MCP client class
- **Used by**:
  - `src/extension.ts` - To create and manage the MCP client
  - `src/ui/webview/panel.ts` - To send requests to the MCP server

### `src/mcp/transport.ts` (Phase 7+)
- **Purpose**: Implement optimized transport for MCP
- **Imports**:
  - `@modelcontextprotocol/sdk/types.js` - MCP types
  - `src/mcp/types.ts` - MCP type definitions
  - `src/mcp/interfaces.ts` - MCP interface definitions
- **Exports**:
  - `OptimizedInMemoryTransport` - In-memory transport implementation
  - `createOptimizedTransportPair` - Function to create a pair of transports
- **Used by**:
  - `src/mcp/server.ts` - For server transport
  - `src/mcp/client.ts` - For client transport

### `src/mcp/registry.ts` (Phase 7+)
- **Purpose**: Manage external MCP servers
- **Imports**:
  - `vscode` - VS Code API
  - `@modelcontextprotocol/sdk/client/index.js` - MCP client SDK
  - `src/mcp/types.ts` - MCP type definitions
  - `src/mcp/interfaces.ts` - MCP interface definitions
- **Exports**:
  - `ExternalServerRegistry` - Registry class for external MCP servers
- **Used by**:
  - `src/mcp/server.ts` - To manage external server connections

## AI Agents

### `src/agents/framework/agent-system.ts` (Phase 8+)
- **Purpose**: Core agent system implementation
- **Imports**:
  - `src/parallel/worker-pool.ts` - For parallel processing
  - `src/llm/providers/*.ts` - For LLM providers
  - Other agent framework files
- **Exports**:
  - `X10sionAgentSystem` - Main agent system class
- **Used by**:
  - `src/extension.ts` - To create and manage the agent system
  - `src/mcp/server.ts` - To integrate agents with MCP

### `src/agents/framework/agent-factory.ts` (Phase 8+)
- **Purpose**: Factory for creating agents
- **Imports**:
  - `src/agents/base-agent.ts` - Base agent class
  - Agent implementation files
- **Exports**:
  - `AgentFactory` - Agent factory class
- **Used by**:
  - `src/agents/framework/agent-system.ts` - To create agents

### `src/agents/framework/agent-registry.ts` (Phase 8+)
- **Purpose**: Registry for discovering agents
- **Imports**:
  - `src/agents/base-agent.ts` - Base agent class
- **Exports**:
  - `AgentRegistry` - Agent registry class
- **Used by**:
  - `src/agents/framework/agent-system.ts` - To register and discover agents

### `src/agents/framework/agent-orchestrator.ts` (Phase 8+)
- **Purpose**: Coordinate agent interactions and workflows (refactored for modularity)
- **Imports**:
  - `src/agents/base-agent.ts` - Base agent class
  - `src/agents/framework/agent-system.ts` - Agent system
  - `src/agents/framework/orchestrator/types.ts` - Type definitions
  - `src/agents/framework/orchestrator/workflow-executor.ts` - Workflow execution
  - `src/agents/framework/orchestrator/step-executor.ts` - Step execution
- **Exports**:
  - `AgentOrchestrator` - Agent orchestrator class
  - Re-exports types from orchestrator modules
- **Used by**:
  - `src/agents/framework/agent-system.ts` - To coordinate agent interactions

### `src/agents/framework/orchestrator/types.ts` (Phase 8+)
- **Purpose**: Type definitions for the orchestrator system
- **Exports**:
  - `Workflow` - Workflow interface
  - `WorkflowStep` - Workflow step interface
  - `WorkflowResult` - Workflow result interface
  - `StepResult` - Step result interface
  - `WorkflowStatus` - Workflow status enum
- **Used by**:
  - All orchestrator modules

### `src/agents/framework/orchestrator/workflow-executor.ts` (Phase 8+)
- **Purpose**: Workflow execution logic for sequential and parallel workflows
- **Imports**:
  - `src/agents/framework/orchestrator/types.ts` - Type definitions
  - `src/agents/framework/orchestrator/step-executor.ts` - Step execution
  - `src/agents/framework/orchestrator/dependency-resolver.ts` - Dependency resolution
- **Exports**:
  - `WorkflowExecutor` - Workflow execution class
- **Used by**:
  - `src/agents/framework/agent-orchestrator.ts` - Main orchestrator

### `src/agents/framework/orchestrator/step-executor.ts` (Phase 8+)
- **Purpose**: Individual step execution logic with timeout and retry handling
- **Imports**:
  - `src/agents/framework/orchestrator/types.ts` - Type definitions
  - `src/agents/framework/agent-system.ts` - Agent system
- **Exports**:
  - `StepExecutor` - Step execution class
- **Used by**:
  - `src/agents/framework/orchestrator/workflow-executor.ts` - Workflow execution

### `src/agents/framework/orchestrator/dependency-resolver.ts` (Phase 8+)
- **Purpose**: Dependency resolution and step ordering for workflows
- **Imports**:
  - `src/agents/framework/orchestrator/types.ts` - Type definitions
- **Exports**:
  - `DependencyResolver` - Dependency resolution class
- **Used by**:
  - `src/agents/framework/orchestrator/workflow-executor.ts` - Workflow execution

### `src/agents/framework/agent-memory-manager.ts` (Phase 8+)
- **Purpose**: Manage agent memory
- **Imports**:
  - `src/agents/base-agent.ts` - Base agent class
- **Exports**:
  - `AgentMemoryManager` - Agent memory manager class
- **Used by**:
  - `src/agents/base-agent.ts` - To manage agent memory

### `src/agents/base-agent.ts` (Phase 8+)
- **Purpose**: Base class for all agents
- **Imports**:
  - `events` - Node.js events
  - `src/parallel/worker-pool.ts` - For parallel processing
  - `src/agents/framework/agent-memory-manager.ts` - For memory management
- **Exports**:
  - `BaseAgent` - Abstract base agent class
- **Used by**:
  - All agent implementation files
  - `src/agents/human-in-the-loop-agent.ts` - For human-in-the-loop agent

### `src/agents/code-generation-agent.ts` (Phase 8+)
- **Purpose**: Main code generation agent (refactored for modularity)
- **Imports**:
  - `src/agents/base-agent.ts` - Base agent class
  - `src/agents/code-generation/language-detector.ts` - Language detection utilities
  - `src/agents/code-generation/code-parser.ts` - Code parsing utilities
  - `src/agents/code-generation/prompt-builder.ts` - Prompt building utilities
- **Exports**:
  - `CodeGenerationAgent` - Code generation agent implementation
- **Used by**:
  - `src/agents/framework/agent-system.ts` - Agent system

### `src/agents/code-generation/language-detector.ts` (Phase 8+)
- **Purpose**: Language detection and compatibility utilities
- **Exports**:
  - `inferLanguage` - Infer programming language from text
  - `isCompatibleLanguage` - Check language compatibility
  - `getLanguageConfig` - Get language-specific configuration
  - `getLanguageFeatures` - Get language feature support
- **Used by**:
  - `src/agents/code-generation-agent.ts` - Main code generation agent

### `src/agents/code-generation/code-parser.ts` (Phase 8+)
- **Purpose**: Code parsing and extraction utilities
- **Exports**:
  - `extractCodeBlocks` - Extract code blocks from text
  - `extractImports` - Extract import statements
  - `extractDependencies` - Extract external dependencies
  - `extractExplanation` - Extract code explanations
- **Used by**:
  - `src/agents/code-generation-agent.ts` - Main code generation agent

### `src/agents/code-generation/prompt-builder.ts` (Phase 8+)
- **Purpose**: Prompt building utilities for code generation
- **Exports**:
  - `createBasePrompt` - Create base code generation prompt
  - `createTestPrompt` - Create test generation prompt
  - `createDocumentationPrompt` - Create documentation prompt
  - `createRefactoringPrompt` - Create refactoring prompt
- **Used by**:
  - `src/agents/code-generation-agent.ts` - Main code generation agent

### `src/human-in-the-loop/agent.ts`
- **Purpose**: Implement human-in-the-loop agent for AI operations
- **Imports**:
  - `vscode` - VS Code API
  - `events` - Node.js events
- **Exports**:
  - `HumanInTheLoopAgent` class - Main agent implementation
  - `InterventionLevel` enum - Levels of human intervention
  - `InterventionRequest` interface - Request for human intervention
  - `InterventionResponse` interface - Response to intervention request
  - `CompletedIntervention` interface - Completed intervention details
- **Used by**:
  - `src/extension.ts` - For initializing the agent
  - `src/mcp/tools/shell-command.ts` - For shell command approval
  - `src/mcp/tools/file-system.ts` - For file system operation approval
  - Other AI agents - For requesting human intervention

### `src/human-in-the-loop/config.ts` (Future implementation)
- **Purpose**: Configuration for human-in-the-loop agent
- **Imports**:
  - `vscode` - VS Code API
  - `src/human-in-the-loop/agent.ts` - For agent interfaces and enums
- **Exports**:
  - `getDefaultHumanInTheLoopConfig` function - Default configuration
  - `getPackageJsonConfig` function - Package.json configuration helper
- **Used by**:
  - `src/extension.ts` - For initializing the agent with configuration

### `src/agents/prompt-enhancement-agent.ts` (Phase 9+)
- **Purpose**: Enhance prompts for better LLM responses (refactored for modularity)
- **Imports**:
  - `src/agents/base-agent.ts` - Base agent class
  - `src/agents/prompt-enhancement/prompt-analyzer.ts` - Prompt analysis
  - `src/agents/prompt-enhancement/context-prioritizer.ts` - Context prioritization
  - `src/agents/prompt-enhancement/token-manager.ts` - Token management
- **Exports**:
  - `PromptEnhancementAgent` - Prompt enhancement agent implementation
  - Re-exports types from enhancement modules
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/agents/code-generation-agent.ts` - Code generation agent

### `src/agents/prompt-enhancement/prompt-analyzer.ts` (Phase 9+)
- **Purpose**: Prompt analysis utilities for understanding intent and extracting keywords
- **Exports**:
  - `PromptAnalyzer` - Prompt analysis class
  - `PromptAnalysisResult` - Analysis result interface
- **Used by**:
  - `src/agents/prompt-enhancement-agent.ts` - Main prompt enhancement agent

### `src/agents/prompt-enhancement/context-prioritizer.ts` (Phase 9+)
- **Purpose**: Context prioritization and scoring based on relevance
- **Exports**:
  - `ContextPrioritizer` - Context prioritization class
  - `ScoredContextItem` - Scored context item interface
  - `ContextPrioritizationStrategy` - Prioritization strategy type
- **Used by**:
  - `src/agents/prompt-enhancement-agent.ts` - Main prompt enhancement agent

### `src/agents/prompt-enhancement/token-manager.ts` (Phase 9+)
- **Purpose**: Token counting, budget management, and content compression
- **Exports**:
  - `TokenManager` - Token management class
  - `TokenBudget` - Token budget interface
- **Used by**:
  - `src/agents/prompt-enhancement-agent.ts` - Main prompt enhancement agent

### `src/agents/code-reviewer-agent.ts` (Phase 9+)
- **Purpose**: Review code for quality and issues
- **Imports**:
  - `src/agents/base-agent.ts` - Base agent class
  - `src/llm/providers/base-provider.ts` - LLM provider interface
- **Exports**:
  - `CodeReviewerAgent` - Code reviewer agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/mcp/server.ts` - To register the agent with MCP

### `src/agents/documentation-agent.ts` (Phase 9+)
- **Purpose**: Generate and update documentation
- **Imports**:
  - `src/agents/base-agent.ts` - Base agent class
  - `src/llm/providers/base-provider.ts` - LLM provider interface
- **Exports**:
  - `DocumentationAgent` - Documentation agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/mcp/server.ts` - To register the agent with MCP

### `src/agents/dependency-management-agent.ts` (Phase 9+)
- **Purpose**: Manage dependencies and keep them up-to-date
- **Imports**:
  - `src/agents/base-agent.ts` - Base agent class
  - `src/llm/providers/base-provider.ts` - LLM provider interface
- **Exports**:
  - `DependencyManagementAgent` - Dependency management agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/mcp/server.ts` - To register the agent with MCP

### `src/agents/ux-improvement-agent.ts` (Phase 12+)
- **Purpose**: Collect and analyze user interactions to suggest UI/UX improvements
- **Imports**:
  - `vscode` - VS Code API
  - `src/agents/base-agent.ts` - Base agent class
- **Exports**:
  - `UXImprovementAgent` - UX improvement agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/extension.ts` - To track UI events and suggest improvements

### `src/agents/integration-agent.ts` (Phase 12+)
- **Purpose**: Manage integration between components
- **Imports**:
  - `vscode` - VS Code API
  - `src/agents/base-agent.ts` - Base agent class
  - `src/mcp/types.ts` - For MCP component types
- **Exports**:
  - `IntegrationAgent` - Integration agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/extension.ts` - To manage component integrations

### `src/agents/feedback-collection-agent.ts` (Phase 12+)
- **Purpose**: Collect and process user feedback
- **Imports**:
  - `vscode` - VS Code API
  - `src/agents/base-agent.ts` - Base agent class
- **Exports**:
  - `FeedbackCollectionAgent` - Feedback collection agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/extension.ts` - To collect and process feedback

### `src/agents/system-optimization-agent.ts` (Phase 12+)
- **Purpose**: Monitor system performance and suggest optimizations
- **Imports**:
  - `vscode` - VS Code API
  - `src/agents/base-agent.ts` - Base agent class
  - `os` - Node.js OS utilities
  - `process` - Node.js process utilities
- **Exports**:
  - `SystemOptimizationAgent` - System optimization agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/extension.ts` - To monitor performance and suggest optimizations

### `src/agents/telemetry-agent.ts` (Phase 13+)
- **Purpose**: Collect and manage telemetry data
- **Imports**:
  - `vscode` - VS Code API
  - `src/agents/base-agent.ts` - Base agent class
  - `crypto` - Node.js crypto utilities
- **Exports**:
  - `TelemetryAgent` - Telemetry agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/extension.ts` - To collect and manage telemetry data

### `src/agents/feedback-analysis-agent.ts` (Phase 13+)
- **Purpose**: Analyze user feedback and identify improvement areas
- **Imports**:
  - `vscode` - VS Code API
  - `src/agents/base-agent.ts` - Base agent class
- **Exports**:
  - `FeedbackAnalysisAgent` - Feedback analysis agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/extension.ts` - To analyze feedback and suggest improvements

### `src/agents/upgrade-agent.ts` (Phase 13+)
- **Purpose**: Manage automated upgrades
- **Imports**:
  - `vscode` - VS Code API
  - `src/agents/base-agent.ts` - Base agent class
  - `fs` - Node.js file system
  - `path` - Node.js path
- **Exports**:
  - `UpgradeAgent` - Upgrade agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/extension.ts` - To manage automated upgrades

### `src/agents/model-tuning-agent.ts` (Phase 13+)
- **Purpose**: Fine-tune AI models based on usage patterns
- **Imports**:
  - `vscode` - VS Code API
  - `src/agents/base-agent.ts` - Base agent class
  - `fs` - Node.js file system
  - `path` - Node.js path
- **Exports**:
  - `ModelTuningAgent` - Model tuning agent implementation
- **Used by**:
  - `src/agents/framework/agent-factory.ts` - To create the agent
  - `src/extension.ts` - To manage model fine-tuning

## Parallel Processing

### `src/parallel/worker-pool.ts` (Phase 6+)
- **Purpose**: Manage worker threads for parallel processing
- **Imports**:
  - `worker_threads` - Node.js worker threads
  - `os` - Node.js OS utilities
  - `src/parallel/task-scheduler.ts` - For task scheduling
  - `src/parallel/resource-monitor.ts` - For resource monitoring
- **Exports**:
  - `WorkerPool` - Worker pool implementation
- **Used by**:
  - `src/extension.ts` - To create and manage the worker pool
  - `src/agents/framework/agent-system.ts` - For agent parallelization
  - `src/rag/system.ts` - For parallel RAG operations
  - `src/mcp/server.ts` - For parallel MCP operations

### `src/parallel/worker.js` (Phase 6+)
- **Purpose**: Worker thread implementation
- **Imports**:
  - `worker_threads` - Node.js worker threads
- **Used by**:
  - `src/parallel/worker-pool.ts` - As worker script

### `src/parallel/task-scheduler.ts` (Phase 6+)
- **Purpose**: Schedule and prioritize tasks
- **Imports**:
  - `src/parallel/resource-monitor.ts` - For resource monitoring
- **Exports**:
  - `TaskScheduler` - Task scheduler implementation
- **Used by**:
  - `src/parallel/worker-pool.ts` - To schedule tasks
  - `src/parallel/background-worker-manager.ts` - To schedule background tasks

### `src/parallel/resource-monitor.ts` (Phase 6+)
- **Purpose**: Monitor CPU and memory usage
- **Imports**:
  - `os` - Node.js OS utilities
- **Exports**:
  - `ResourceMonitor` - Resource monitor implementation
- **Used by**:
  - `src/parallel/worker-pool.ts` - To monitor resource usage
  - `src/parallel/task-scheduler.ts` - To make scheduling decisions
  - `src/parallel/background-worker-manager.ts` - To manage background workers

### `src/parallel/background-worker-manager.ts` (Phase 11+)
- **Purpose**: Manage background workers for efficient resource usage
- **Imports**:
  - `src/parallel/worker-pool.ts` - For worker pool
  - `src/parallel/task-scheduler.ts` - For task scheduling
  - `src/parallel/resource-monitor.ts` - For resource monitoring
- **Exports**:
  - `BackgroundWorkerManager` - Background worker manager implementation
- **Used by**:
  - `src/extension.ts` - To create and manage background workers

## Monitoring Systems

### `src/monitoring/llm-monitor.ts` (Phase 10+)
- **Purpose**: Re-export module for backward compatibility
- **Exports**:
  - All types and classes from `src/monitoring/llm-monitor/index.ts`
- **Used by**:
  - Legacy code that imports from the old location

### `src/monitoring/llm-monitor/index.ts` (Phase 10+)
- **Purpose**: Main LLM monitoring system (modular architecture)
- **Imports**:
  - `src/monitoring/llm-monitor/event-handler.ts` - Event handling service
  - `src/monitoring/llm-monitor/agent-manager.ts` - Agent management service
  - `src/monitoring/llm-monitor/metrics.ts` - Metrics service
  - `src/monitoring/llm-monitor/persistence.ts` - Persistence service
  - Other monitoring services
- **Exports**:
  - `LLMMonitor` - Main LLM monitor class
- **Used by**:
  - `src/agents/framework/agent-system.ts` - To monitor agent outputs
  - `src/llm/ollama.ts` - To monitor LLM outputs

### `src/monitoring/llm-monitor/event-handler.ts` (Phase 10+)
- **Purpose**: Handle event emission and subscription for LLM monitoring
- **Exports**:
  - `EventHandlerService` - Event handling service
- **Used by**:
  - `src/monitoring/llm-monitor/index.ts` - For event management

### `src/monitoring/llm-monitor/agent-manager.ts` (Phase 10+)
- **Purpose**: Manage agent registration and agent-related functionality
- **Exports**:
  - `AgentManagerService` - Agent management service
- **Used by**:
  - `src/monitoring/llm-monitor/index.ts` - For agent management

### `src/monitoring/llm-monitor/metrics.ts` (Phase 10+)
- **Purpose**: Main metrics service (delegates to specialized services)
- **Imports**:
  - `src/monitoring/llm-monitor/time-stats.ts` - Time-based statistics
  - `src/monitoring/llm-monitor/agent-metrics.ts` - Agent-specific metrics
- **Exports**:
  - `MetricsService` - Main metrics service
- **Used by**:
  - `src/monitoring/llm-monitor/index.ts` - For metrics calculation

### `src/monitoring/llm-monitor/time-stats.ts` (Phase 10+)
- **Purpose**: Calculate time-based statistics for LLM monitoring
- **Exports**:
  - `TimeStatsService` - Time-based statistics service
- **Used by**:
  - `src/monitoring/llm-monitor/metrics.ts` - For time-based calculations

### `src/monitoring/llm-monitor/agent-metrics.ts` (Phase 10+)
- **Purpose**: Calculate agent-specific metrics and behavior analysis
- **Exports**:
  - `AgentMetricsService` - Agent-specific metrics service
- **Used by**:
  - `src/monitoring/llm-monitor/metrics.ts` - For agent-specific calculations

### `src/monitoring/terminal-monitor.ts` (Phase 10+)
- **Purpose**: Monitor terminal output in real-time
- **Imports**:
  - `vscode` - VS Code API
  - `src/monitoring/pattern-matcher.ts` - Pattern matching service
  - `src/monitoring/buffer-manager.ts` - Buffer management service
- **Exports**:
  - `TerminalMonitor` - Terminal monitor implementation
- **Used by**:
  - `src/extension.ts` - To monitor terminal output

### `src/monitoring/pattern-matcher.ts` (Phase 10+)
- **Purpose**: Pattern matching service for detecting issues in terminal output
- **Exports**:
  - `PatternMatcherService` - Pattern matching service
- **Used by**:
  - `src/monitoring/terminal-monitor.ts` - For pattern detection

### `src/monitoring/buffer-manager.ts` (Phase 10+)
- **Purpose**: Buffer management service for terminal monitoring
- **Exports**:
  - `BufferManagerService` - Buffer management service
- **Used by**:
  - `src/monitoring/terminal-monitor.ts` - For buffer management

## File Management

### `src/file-management/file-registry.ts` (Phase 10+)
- **Purpose**: Track files created or modified by AI agents
- **Imports**:
  - `vscode` - VS Code API
- **Exports**:
  - `FileRegistry` - File registry implementation
- **Used by**:
  - `src/agents/framework/agent-system.ts` - To track agent file operations
  - `src/file-management/content-manager.ts` - To check file existence

### `src/file-management/content-manager.ts` (Phase 10+)
- **Purpose**: Ensure consistent file structure and content
- **Imports**:
  - `vscode` - VS Code API
  - `src/file-management/file-registry.ts` - For file registry
- **Exports**:
  - `ContentManager` - Content manager implementation
- **Used by**:
  - `src/agents/framework/agent-system.ts` - To manage file content

## Worker Implementations

### `src/workers/documentation-worker.js` (Phase 11+)
- **Purpose**: Update documentation files
- **Imports**:
  - `worker_threads` - Node.js worker threads
  - `fs` - Node.js file system
  - `path` - Node.js path
- **Used by**:
  - `src/parallel/background-worker-manager.ts` - As a background worker

## Optimization Techniques

### `src/optimization/lazy-loader.ts` (Phase 11+)
- **Purpose**: Defer loading of non-critical components
- **Exports**:
  - `LazyLoader` - Lazy loader implementation
- **Used by**:
  - `src/extension.ts` - To lazy load components

### `src/optimization/memory-optimizer.ts` (Phase 11+)
- **Purpose**: Minimize memory usage
- **Exports**:
  - `MemoryOptimizer` - Memory optimizer implementation
- **Used by**:
  - `src/extension.ts` - To optimize memory usage

### `src/optimization/incremental-processor.ts` (Phase 11+)
- **Purpose**: Break large tasks into smaller chunks
- **Exports**:
  - `IncrementalProcessor` - Incremental processor implementation
- **Used by**:
  - `src/extension.ts` - To process large tasks incrementally

## Documentation

### `README.md`
- **Purpose**: Project overview and development guide
- **Referenced by**:
  - All development documentation

### `docs/dev_guide/development_phases.md`
- **Purpose**: Overview of all development phases
- **Referenced by**:
  - All phase documentation

### `docs/dev_guide/devPhaseX/devPhaseX.md`
- **Purpose**: Overview of development phase X
- **Referenced by**:
  - `docs/dev_guide/devPhaseX/devPhaseX_tasks.md` - For task details

### `docs/dev_guide/devPhaseX/devPhaseX_tasks.md`
- **Purpose**: Detailed tasks for development phase X
- **References**:
  - `docs/dev_guide/devPhaseX/devPhaseX.md` - For phase overview

### `docs/dev_guide/devPhase12/devPhase12.md`
- **Purpose**: Overview of development phase 12 (AI Agent Framework for UX and Integration)
- **Referenced by**:
  - `docs/dev_guide/devPhase12/devPhase12_tasks.md` - For task details
  - `src/agents/ux-improvement-agent.ts` - For UX improvement agent implementation
  - `src/agents/integration-agent.ts` - For integration agent implementation
  - `src/agents/feedback-collection-agent.ts` - For feedback collection agent implementation
  - `src/agents/system-optimization-agent.ts` - For system optimization agent implementation

### `docs/dev_guide/devPhase12/devPhase12_tasks.md`
- **Purpose**: Detailed tasks for development phase 12
- **References**:
  - `docs/dev_guide/devPhase12/devPhase12.md` - For phase overview

### `docs/dev_guide/devPhase13/devPhase13.md`
- **Purpose**: Overview of development phase 13 (Continuous Learning and System Improvement)
- **Referenced by**:
  - `docs/dev_guide/devPhase13/devPhase13_tasks.md` - For task details
  - `src/agents/telemetry-agent.ts` - For telemetry agent implementation
  - `src/agents/feedback-analysis-agent.ts` - For feedback analysis agent implementation
  - `src/agents/upgrade-agent.ts` - For upgrade agent implementation
  - `src/agents/model-tuning-agent.ts` - For model fine-tuning agent implementation

### `docs/dev_guide/devPhase13/devPhase13_tasks.md`
- **Purpose**: Detailed tasks for development phase 13
- **References**:
  - `docs/dev_guide/devPhase13/devPhase13.md` - For phase overview

### `docs/dev_guide/phase_restructuring.md`
- **Purpose**: Plan for restructuring development phases
- **Referenced by**:
  - `docs/dev_guide/development_phases.md` - For phase overview

### `docs/features/human-in-the-loop.md`
- **Purpose**: Documentation for human-in-the-loop functionality
- **Referenced by**:
  - `docs/user_guide/features.md` - For feature documentation
- **References**:
  - `src/human-in-the-loop/agent.ts` - For implementation details
  - `src/human-in-the-loop/config.ts` - For configuration details (future)
  - `src/human-in-the-loop/view.ts` - For UI details (future)

## Guidelines Files

### `x10sion_general_guidelines.md`
- **Purpose**: General AI assistant guidelines
- **Referenced by**:
  - `src/context/guidelines.ts` - For context enhancement

### `x10sion_project_guidelines.md`
- **Purpose**: Project-specific guidelines
- **Referenced by**:
  - `src/context/guidelines.ts` - For context enhancement

## Knowledge Base

### Files in `knowledge_base/code_patterns/`
- **Purpose**: Common code patterns for RAG system
- **Referenced by**:
  - `src/rag/indexing.ts` - For RAG indexing

### Files in `knowledge_base/best_practices/`
- **Purpose**: Best practices for RAG system
- **Referenced by**:
  - `src/rag/indexing.ts` - For RAG indexing

### Files in `knowledge_base/examples/`
- **Purpose**: Example files for testing and documentation
- **Referenced by**:
  - `testMethod.md` - For test methodology
  - `src/test/suite/extension.test.ts` - For test cases

### `knowledge_base/examples/example_queries.md`
- **Purpose**: Example queries for testing X10sion's AI capabilities
- **Referenced by**:
  - `testMethod.md` - For test methodology
  - `src/test/suite/extension.test.ts` - For test cases
  - `knowledge_base/examples/example_responses.md` - For corresponding responses

### `knowledge_base/examples/example_responses.md`
- **Purpose**: Example responses demonstrating X10sion's AI capabilities
- **Referenced by**:
  - `testMethod.md` - For test methodology
  - `src/test/suite/extension.test.ts` - For test cases

### `knowledge_base/best_practices/vscode_extension_best_practices.md`
- **Purpose**: Documentation on VS Code extension best practices
- **Referenced by**:
  - `docs/dev_guide/devPhase0/devPhase0.md` - For Phase 0 development
  - `docs/dev_guide/devPhase1/devPhase1.md` - For Phase 1 development
  - `docs/dev_guide/devPhase9/devPhase9.md` - For Phase 9 development

### `knowledge_base/best_practices/vscode_native_ui_best_practices.md`
- **Purpose**: Documentation on VS Code native UI best practices
- **Referenced by**:
  - `docs/dev_guide/devPhase2/devPhase2.md` - For Phase 2 development
  - `docs/dev_guide/devPhase3/devPhase3.md` - For Phase 3 development
  - `docs/dev_guide/devPhase9/devPhase9.md` - For Phase 9 development
  - `src/extension.ts` - For native UI implementation
  - `src/chatView.ts` - For chat view implementation
  - `src/mcpComponents.ts` - For MCP components view implementation

### `knowledge_base/best_practices/typescript_best_practices.md`
- **Purpose**: Documentation on TypeScript best practices
- **Referenced by**:
  - `docs/dev_guide/devPhase0/devPhase0.md` - For Phase 0 development
  - `docs/dev_guide/devPhase1/devPhase1.md` - For Phase 1 development
  - `src/extension.ts` - For TypeScript implementation

### `knowledge_base/best_practices/dependency_management.md`
- **Purpose**: Documentation on dependency management best practices
- **Referenced by**:
  - `docs/dev_guide/devPhase6/devPhase6.md` - For Phase 6 development
  - `docs/dev_guide/devPhase10/devPhase10.md` - For Phase 10 development

### `knowledge_base/best_practices/ollama_integration_best_practices.md`
- **Purpose**: Documentation on Ollama integration best practices
- **Referenced by**:
  - `docs/dev_guide/devPhase2/devPhase2.md` - For Phase 2 development
  - `src/ollama/api.ts` - For Ollama API implementation
  - `src/ollama/client.ts` - For Ollama client implementation

### `knowledge_base/best_practices/rag_implementation_best_practices.md`
- **Purpose**: Documentation on RAG implementation best practices
- **Referenced by**:
  - `docs/dev_guide/devPhase4/devPhase4.md` - For Phase 4 development
  - `docs/dev_guide/devPhase7/devPhase7.md` - For Phase 7 development
  - `src/rag/indexing.ts` - For RAG indexing implementation
  - `src/rag/retrieval.ts` - For RAG retrieval implementation

### `knowledge_base/best_practices/mcp_implementation_best_practices.md`
- **Purpose**: Documentation on MCP implementation best practices
- **Referenced by**:
  - `docs/dev_guide/devPhase6/devPhase6.md` - For Phase 6 development
  - `src/mcp/server.ts` - For MCP server implementation
  - `src/mcp/client.ts` - For MCP client implementation
  - `src/mcp/transport.ts` - For MCP transport implementation

### `knowledge_base/best_practices/ai_agent_marketplace_ui_ux.md`
- **Purpose**: Documentation on AI agent marketplace UI/UX best practices
- **Referenced by**:
  - `docs/dev_guide/devPhase10/devPhase10.md` - For Phase 10 development
  - `src/marketplace/ui.ts` - For marketplace UI implementation
  - `src/webview/marketplace-panel.ts` - For marketplace webview implementation

### Files in `knowledge_base/security/`
- **Purpose**: Security best practices for RAG system
- **Referenced by**:
  - `src/rag/indexing.ts` - For RAG indexing

### Files in `knowledge_base/architecture/`
- **Purpose**: Architecture documentation for RAG system
- **Referenced by**:
  - `src/rag/indexing.ts` - For RAG indexing

### `knowledge_base/architecture/ai_agent_implementation.md`
- **Purpose**: Documentation on AI agent implementation details
- **Referenced by**:
  - `docs/dev_guide/devPhase6/devPhase6.md` - For Phase 6 development
  - `docs/dev_guide/devPhase8/devPhase8.md` - For Phase 8 development
  - `docs/dev_guide/devPhase12/devPhase12.md` - For Phase 12 development
  - `docs/dev_guide/devPhase13/devPhase13.md` - For Phase 13 development
  - `src/agents/framework/agent-system.ts` - For agent system implementation

### `knowledge_base/architecture/ai_agents.md`
- **Purpose**: Documentation on AI agent architecture overview
- **Referenced by**:
  - `docs/dev_guide/devPhase6/devPhase6.md` - For Phase 6 development
  - `docs/dev_guide/devPhase8/devPhase8.md` - For Phase 8 development
  - `docs/dev_guide/devPhase12/devPhase12.md` - For Phase 12 development
  - `docs/dev_guide/devPhase13/devPhase13.md` - For Phase 13 development
  - `src/agents/framework/agent-system.ts` - For agent system implementation

### `knowledge_base/architecture/agi_integration.md`
- **Purpose**: Documentation on AGI integration strategies
- **Referenced by**:
  - `docs/dev_guide/devPhase6/devPhase6.md` - For Phase 6 development
  - `docs/dev_guide/devPhase11/devPhase11.md` - For Phase 11 development
  - `docs/dev_guide/devPhase12/devPhase12.md` - For Phase 12 development
  - `docs/dev_guide/devPhase13/devPhase13.md` - For Phase 13 development
  - `src/agents/framework/agent-system.ts` - For AGI-aligned agent capabilities
  - `src/mcp/server.ts` - For AGI-ready MCP implementation
  - `src/parallel/background-worker-manager.ts` - For AGI-oriented background processing

### `knowledge_base/architecture/background_worker_system.md`
- **Purpose**: Documentation on background worker system
- **Referenced by**:
  - `docs/dev_guide/devPhase6/devPhase6.md` - For Phase 6 development
  - `src/parallel/background-worker-manager.ts` - For background worker implementation
  - `src/parallel/task-scheduler.ts` - For task scheduling implementation

### `knowledge_base/architecture/mcp_components.md`
- **Purpose**: Documentation on MCP components details
- **Referenced by**:
  - `docs/dev_guide/devPhase6/devPhase6.md` - For Phase 6 development
  - `src/mcp/server.ts` - For MCP server implementation
  - `src/mcp/client.ts` - For MCP client implementation
  - `src/mcp/components/agent.ts` - For MCP agent implementation
  - `src/mcp/components/tool.ts` - For MCP tool implementation
  - `src/mcp/components/resource.ts` - For MCP resource implementation
  - `src/mcp/components/prompt.ts` - For MCP prompt implementation

### `knowledge_base/architecture/optimization_techniques.md`
- **Purpose**: Documentation on optimization techniques
- **Referenced by**:
  - `docs/dev_guide/devPhase6/devPhase6.md` - For Phase 6 development
  - `src/optimization/memory-optimizer.ts` - For memory optimization implementation
  - `src/optimization/performance-monitor.ts` - For performance monitoring implementation

### `knowledge_base/architecture/webassembly_integration.md`
- **Purpose**: Documentation on WebAssembly integration
- **Referenced by**:
  - `docs/dev_guide/devPhase7/devPhase7.md` - For Phase 7 development
  - `src/rag/indexing.ts` - For RAG indexing
  - `src/optimization/memory-optimizer.ts` - For optimization techniques
