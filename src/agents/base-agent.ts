/**
 * Base Agent for X10sion
 *
 * This module defines the base class for all AI agents in the X10sion system.
 * Refactored for May 2025 optimization standards (300-500 lines per file).
 */

import { WorkerPool, TaskPriority } from '../parallel/worker-pool.js';
import { LLMMonitor } from '../monitoring/llm-monitor.js';
import { LanguageModelProvider } from '../llm/providers/base-provider.js';
import * as vscode from 'vscode';

// Import modular components
import {
    AgentStatus,
    AgentMemoryItem,
    AgentTask,
    AgentOptions,
    LLMRequestOptions
} from './base/types.js';
import { MemoryManager } from './base/memory-manager.js';
import { TaskManager } from './base/task-manager.js';
import { LLMIntegration } from './base/llm-integration.js';
import { CollaborationManager, IBaseAgent } from './base/collaboration-manager.js';

// Re-export types for external use
export {
    AgentStatus,
    AgentMemoryItem,
    AgentTask,
    AgentOptions
} from './base/types.js';

/**
 * Base Agent class
 *
 * All AI agents in the system should extend this class.
 * Refactored to use modular components for better maintainability.
 */
export class BaseAgent implements IBaseAgent {
    protected id: string;
    protected name: string;
    protected description: string;
    protected workerPool: WorkerPool;
    protected options: AgentOptions;
    protected status: AgentStatus = AgentStatus.IDLE;
    protected eventEmitter: vscode.EventEmitter<any> = new vscode.EventEmitter<any>();
    protected disposables: vscode.Disposable[] = [];
    protected lastActivity: number = Date.now();

    // Modular components
    protected memoryManager: MemoryManager;
    protected taskManager: TaskManager;
    protected llmIntegration: LLMIntegration;
    protected collaborationManager: CollaborationManager;

    /**
     * Constructor for BaseAgent
     */
    constructor(
        id: string,
        name: string,
        description: string,
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor,
        llmProvider: LanguageModelProvider,
        options: AgentOptions = {}
    ) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.workerPool = workerPool;

        // Set default options
        this.options = {
            maxMemoryItems: 100,
            contextWindowSize: 4000,
            maxTokens: 1000,
            temperature: 0.7,
            useCache: true,
            cacheExpiration: 3600000, // 1 hour
            debugMode: false,
            timeout: 30000, // 30 seconds
            retryCount: 3,
            retryDelay: 1000, // 1 second
            ...options
        };

        // Initialize modular components
        this.memoryManager = new MemoryManager(this.id, this.options);
        this.llmIntegration = new LLMIntegration(this.id, llmProvider, llmMonitor, this.memoryManager, this.options);
        this.collaborationManager = new CollaborationManager(this.id, this.memoryManager, this.eventEmitter);
        this.taskManager = new TaskManager(
            this.id,
            this.options,
            this.memoryManager,
            this.eventEmitter,
            this.execute.bind(this)
        );

        // Register with the LLM monitor
        llmMonitor.registerAgent(this);
    }

    /**
     * Get agent ID
     */
    getId(): string {
        return this.id;
    }

    /**
     * Get agent name
     */
    getName(): string {
        return this.name;
    }

    /**
     * Get agent description
     */
    getDescription(): string {
        return this.description;
    }

    /**
     * Get agent status
     */
    getStatus(): AgentStatus {
        return this.status;
    }

    /**
     * Get the LLM provider used by this agent
     */
    getLLMProvider(): LanguageModelProvider {
        return this.llmIntegration.getLLMProvider();
    }

    /**
     * Set the LLM provider for this agent
     * @param provider New LLM provider
     */
    setLLMProvider(provider: LanguageModelProvider): void {
        this.llmIntegration.setLLMProvider(provider);
    }

    /**
     * Execute a task with the agent
     */
    async execute(task: string, context: any = {}): Promise<any> {
        // This is a placeholder implementation
        // Subclasses should override this method with their specific implementation
        throw new Error('Method not implemented. Subclasses must implement execute().');
    }

    /**
     * Send a request to the LLM with monitoring
     * @param prompt Prompt to send
     * @param options LLM options
     * @returns LLM response
     */
    protected async sendToLLM(prompt: string, options: LLMRequestOptions = {}): Promise<string> {
        return this.llmIntegration.sendToLLM(prompt, options);
    }

    /**
     * Queue a task for execution
     * @param task Task to execute
     * @param context Task context
     * @param priority Task priority
     * @returns Task ID
     */
    queueTask(task: string, context: any = {}, priority: TaskPriority = TaskPriority.NORMAL): string {
        const taskId = this.taskManager.queueTask(task, context, priority);
        this.processTaskQueue();
        return taskId;
    }

    /**
     * Process the task queue
     */
    protected async processTaskQueue(): Promise<void> {
        if (this.status === AgentStatus.BUSY || this.status === AgentStatus.DISPOSED) {
            return;
        }

        this.status = AgentStatus.BUSY;

        try {
            const result = await this.taskManager.processTaskQueue(this.status);
            this.status = result.status;
            this.lastActivity = Date.now();

            // Process the next task if there are more
            if (this.status === AgentStatus.IDLE) {
                setTimeout(() => this.processTaskQueue(), 0);
            }
        } catch (error) {
            this.status = AgentStatus.ERROR;
            this.lastActivity = Date.now();
        }
    }

    /**
     * Get task status
     * @param taskId Task ID
     * @returns Task status or undefined if not found
     */
    getTaskStatus(taskId: string): AgentTask | undefined {
        return this.taskManager.getTaskStatus(taskId);
    }

    /**
     * Add an item to agent memory
     * @param type Memory item type
     * @param content Memory item content
     * @param metadata Memory item metadata
     * @returns Memory item ID
     */
    protected addToMemory(type: string, content: any, metadata?: Record<string, any>): string {
        return this.memoryManager.addToMemory(type, content, metadata);
    }

    /**
     * Get memory items
     * @param type Optional memory item type filter
     * @param limit Optional limit on number of items to return
     * @returns Memory items
     */
    getMemory(type?: string, limit?: number): AgentMemoryItem[] {
        return this.memoryManager.getMemory(type, limit);
    }

    /**
     * Clear memory
     * @param type Optional memory item type filter
     */
    clearMemory(type?: string): void {
        this.memoryManager.clearMemory(type);
    }

    /**
     * Add a collaborating agent
     * @param agent Agent to collaborate with
     */
    addCollaborator(agent: BaseAgent): void {
        this.collaborationManager.addCollaborator(agent);
    }

    /**
     * Remove a collaborating agent
     * @param agent Agent to remove from collaboration
     */
    removeCollaborator(agent: BaseAgent): void {
        this.collaborationManager.removeCollaborator(agent);
    }

    /**
     * Get collaborating agents
     * @returns Set of collaborating agents
     */
    getCollaborators(): Set<IBaseAgent> {
        return this.collaborationManager.getCollaborators();
    }

    /**
     * Send a message to a collaborating agent
     * @param agentId ID of the agent to send the message to
     * @param message Message to send
     * @param metadata Message metadata
     * @returns Promise that resolves when the message is sent
     */
    async sendMessage(agentId: string, message: any, metadata?: Record<string, any>): Promise<void> {
        return this.collaborationManager.sendMessage(agentId, message, metadata);
    }

    /**
     * Receive a message from another agent
     * @param senderId ID of the agent that sent the message
     * @param message Message content
     * @param metadata Message metadata
     */
    async receiveMessage(senderId: string, message: any, metadata?: Record<string, any>): Promise<void> {
        await this.collaborationManager.receiveMessage(senderId, message, metadata);
        // Subclasses should override this method to handle messages
    }

    /**
     * Subscribe to agent events
     * @param listener Event listener
     * @returns Disposable
     */
    onEvent(listener: (event: any) => void): vscode.Disposable {
        return this.eventEmitter.event(listener);
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        this.status = AgentStatus.DISPOSED;

        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        // Clear modular component data
        this.memoryManager.clearMemory();
        this.taskManager.clearAllTasks();

        // Dispose of event emitter
        this.eventEmitter.dispose();
    }
}
