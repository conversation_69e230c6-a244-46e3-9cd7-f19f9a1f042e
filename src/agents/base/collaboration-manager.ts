/**
 * Agent Collaboration Manager
 *
 * Handles agent-to-agent communication and collaboration.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import { AgentMessage } from './types.js';
import { MemoryManager } from './memory-manager.js';
import * as vscode from 'vscode';

// Forward declaration to avoid circular dependency
export interface IBaseAgent {
    getId(): string;
    getName(): string;
    receiveMessage(senderId: string, message: any, metadata?: Record<string, any>): Promise<void>;
}

/**
 * Collaboration Manager for Agents
 * 
 * Manages agent-to-agent communication and collaboration
 */
export class CollaborationManager {
    private agentId: string;
    private collaboratingAgents: Set<IBaseAgent> = new Set();
    private memoryManager: MemoryManager;
    private eventEmitter: vscode.EventEmitter<any>;
    private messageHistory: AgentMessage[] = [];
    private maxMessageHistory: number = 100;

    constructor(
        agentId: string,
        memoryManager: MemoryManager,
        eventEmitter: vscode.EventEmitter<any>
    ) {
        this.agentId = agentId;
        this.memoryManager = memoryManager;
        this.eventEmitter = eventEmitter;
    }

    /**
     * Add a collaborating agent
     * @param agent Agent to collaborate with
     */
    addCollaborator(agent: IBaseAgent): void {
        this.collaboratingAgents.add(agent);

        // Add to memory
        this.memoryManager.addToMemory('collaborator_added', {
            agentId: agent.getId(),
            agentName: agent.getName(),
            timestamp: Date.now()
        });

        // Emit event
        this.eventEmitter.fire({
            type: 'collaborator_added',
            agentId: agent.getId(),
            agentName: agent.getName()
        });
    }

    /**
     * Remove a collaborating agent
     * @param agent Agent to remove from collaboration
     */
    removeCollaborator(agent: IBaseAgent): void {
        const removed = this.collaboratingAgents.delete(agent);

        if (removed) {
            // Add to memory
            this.memoryManager.addToMemory('collaborator_removed', {
                agentId: agent.getId(),
                agentName: agent.getName(),
                timestamp: Date.now()
            });

            // Emit event
            this.eventEmitter.fire({
                type: 'collaborator_removed',
                agentId: agent.getId(),
                agentName: agent.getName()
            });
        }
    }

    /**
     * Get collaborating agents
     * @returns Set of collaborating agents
     */
    getCollaborators(): Set<IBaseAgent> {
        return new Set(this.collaboratingAgents);
    }

    /**
     * Get collaborator by ID
     * @param agentId Agent ID
     * @returns Agent or undefined if not found
     */
    getCollaborator(agentId: string): IBaseAgent | undefined {
        return Array.from(this.collaboratingAgents).find(agent => agent.getId() === agentId);
    }

    /**
     * Send a message to a collaborating agent
     * @param agentId ID of the agent to send the message to
     * @param message Message to send
     * @param metadata Message metadata
     * @returns Promise that resolves when the message is sent
     */
    async sendMessage(agentId: string, message: any, metadata?: Record<string, any>): Promise<void> {
        // Find the agent
        const agent = this.getCollaborator(agentId);

        if (!agent) {
            throw new Error(`Agent ${agentId} not found in collaborators`);
        }

        // Create message record
        const messageRecord: AgentMessage = {
            senderId: this.agentId,
            recipientId: agentId,
            message,
            metadata,
            timestamp: Date.now()
        };

        // Add to message history
        this.addToMessageHistory(messageRecord);

        // Add to memory
        this.memoryManager.addToMemory('message_sent', {
            recipientId: agentId,
            recipientName: agent.getName(),
            message,
            metadata,
            timestamp: messageRecord.timestamp
        });

        // Emit event
        this.eventEmitter.fire({
            type: 'message_sent',
            recipientId: agentId,
            message,
            metadata
        });

        // Send the message
        await agent.receiveMessage(this.agentId, message, metadata);
    }

    /**
     * Broadcast a message to all collaborating agents
     * @param message Message to broadcast
     * @param metadata Message metadata
     * @param excludeAgents Optional list of agent IDs to exclude
     * @returns Promise that resolves when all messages are sent
     */
    async broadcastMessage(
        message: any, 
        metadata?: Record<string, any>, 
        excludeAgents?: string[]
    ): Promise<void> {
        const promises: Promise<void>[] = [];

        for (const agent of this.collaboratingAgents) {
            if (excludeAgents && excludeAgents.includes(agent.getId())) {
                continue;
            }

            promises.push(this.sendMessage(agent.getId(), message, metadata));
        }

        await Promise.all(promises);

        // Add to memory
        this.memoryManager.addToMemory('message_broadcast', {
            message,
            metadata,
            recipientCount: promises.length,
            excludedAgents: excludeAgents || [],
            timestamp: Date.now()
        });
    }

    /**
     * Handle receiving a message from another agent
     * @param senderId ID of the agent that sent the message
     * @param message Message content
     * @param metadata Message metadata
     */
    async receiveMessage(senderId: string, message: any, metadata?: Record<string, any>): Promise<void> {
        // Create message record
        const messageRecord: AgentMessage = {
            senderId,
            recipientId: this.agentId,
            message,
            metadata,
            timestamp: Date.now()
        };

        // Add to message history
        this.addToMessageHistory(messageRecord);

        // Add to memory
        this.memoryManager.addToMemory('message_received', {
            senderId,
            message,
            metadata,
            timestamp: messageRecord.timestamp
        });

        // Emit event
        this.eventEmitter.fire({
            type: 'message_received',
            senderId,
            message,
            metadata
        });
    }

    /**
     * Get message history
     * @param agentId Optional agent ID to filter messages
     * @param limit Optional limit on number of messages
     * @returns Message history
     */
    getMessageHistory(agentId?: string, limit?: number): AgentMessage[] {
        let messages = [...this.messageHistory];

        if (agentId) {
            messages = messages.filter(msg => 
                msg.senderId === agentId || msg.recipientId === agentId
            );
        }

        // Sort by timestamp (newest first)
        messages = messages.sort((a, b) => b.timestamp - a.timestamp);

        if (limit) {
            messages = messages.slice(0, limit);
        }

        return messages;
    }

    /**
     * Add message to history
     * @param message Message to add
     */
    private addToMessageHistory(message: AgentMessage): void {
        this.messageHistory.push(message);

        // Trim history if it exceeds the maximum size
        if (this.messageHistory.length > this.maxMessageHistory) {
            this.messageHistory.shift();
        }
    }

    /**
     * Clear message history
     * @param agentId Optional agent ID to clear messages for specific agent
     */
    clearMessageHistory(agentId?: string): void {
        if (agentId) {
            this.messageHistory = this.messageHistory.filter(msg => 
                msg.senderId !== agentId && msg.recipientId !== agentId
            );
        } else {
            this.messageHistory = [];
        }
    }

    /**
     * Get collaboration statistics
     * @returns Collaboration statistics
     */
    getCollaborationStats(): {
        totalCollaborators: number;
        totalMessagesSent: number;
        totalMessagesReceived: number;
        messagesByAgent: Record<string, { sent: number; received: number }>;
    } {
        const messagesByAgent: Record<string, { sent: number; received: number }> = {};
        let totalSent = 0;
        let totalReceived = 0;

        for (const message of this.messageHistory) {
            if (message.senderId === this.agentId) {
                totalSent++;
                if (!messagesByAgent[message.recipientId]) {
                    messagesByAgent[message.recipientId] = { sent: 0, received: 0 };
                }
                messagesByAgent[message.recipientId].sent++;
            } else if (message.recipientId === this.agentId) {
                totalReceived++;
                if (!messagesByAgent[message.senderId]) {
                    messagesByAgent[message.senderId] = { sent: 0, received: 0 };
                }
                messagesByAgent[message.senderId].received++;
            }
        }

        return {
            totalCollaborators: this.collaboratingAgents.size,
            totalMessagesSent: totalSent,
            totalMessagesReceived: totalReceived,
            messagesByAgent
        };
    }
}
