/**
 * Agent LLM Integration
 *
 * Handles LLM communication for agents.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import { LLMRequestOptions, AgentOptions } from './types.js';
import { LLMMonitor } from '../../monitoring/llm-monitor.js';
import { LanguageModelProvider } from '../../llm/providers/base-provider.js';
import { MemoryManager } from './memory-manager.js';

/**
 * LLM Integration for Agents
 * 
 * Manages LLM communication with monitoring and error handling
 */
export class LLMIntegration {
    private agentId: string;
    private llmProvider: LanguageModelProvider;
    private llmMonitor: LLMMonitor;
    private memoryManager: MemoryManager;
    private options: AgentOptions;

    constructor(
        agentId: string,
        llmProvider: LanguageModelProvider,
        llmMonitor: LLMMonitor,
        memoryManager: MemoryManager,
        options: AgentOptions
    ) {
        this.agentId = agentId;
        this.llmProvider = llmProvider;
        this.llmMonitor = llmMonitor;
        this.memoryManager = memoryManager;
        this.options = options;
    }

    /**
     * Send a request to the LLM with monitoring
     * @param prompt Prompt to send
     * @param options LLM options
     * @returns LLM response
     */
    async sendToLLM(prompt: string, options: LLMRequestOptions = {}): Promise<string> {
        // Set default options
        const llmOptions = {
            temperature: this.options.temperature || 0.7,
            maxTokens: this.options.maxTokens || 1000,
            timeout: this.options.timeout || 30000,
            retryCount: this.options.retryCount || 3,
            retryDelay: this.options.retryDelay || 1000,
            ...options
        };

        // Track the request with the LLM monitor
        const requestId = this.llmMonitor.trackRequest(
            this.llmProvider.getDefaultModel(),
            prompt,
            llmOptions,
            this.agentId
        );

        // Add to memory
        this.memoryManager.addToMemory('llm_request', {
            requestId,
            prompt: prompt.substring(0, 200) + (prompt.length > 200 ? '...' : ''), // Truncate for memory
            model: this.llmProvider.getDefaultModel(),
            options: llmOptions
        });

        let lastError: Error | undefined;

        // Retry logic
        for (let attempt = 0; attempt < llmOptions.retryCount; attempt++) {
            try {
                // Generate completion with timeout
                const startTime = Date.now();
                const completion = await this.generateWithTimeout(prompt, llmOptions);
                const endTime = Date.now();

                // Track the response with the LLM monitor
                this.llmMonitor.trackResponse(
                    requestId,
                    this.llmProvider.getDefaultModel(),
                    completion.text,
                    endTime - startTime,
                    completion.usage?.totalTokens,
                    this.agentId
                );

                // Add to memory
                this.memoryManager.addToMemory('llm_response', {
                    requestId,
                    response: completion.text.substring(0, 200) + (completion.text.length > 200 ? '...' : ''),
                    latency: endTime - startTime,
                    tokens: completion.usage?.totalTokens,
                    attempt: attempt + 1
                });

                return completion.text;
            } catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));

                // Add retry attempt to memory
                this.memoryManager.addToMemory('llm_retry', {
                    requestId,
                    attempt: attempt + 1,
                    error: lastError.message
                });

                // If this is not the last attempt, wait before retrying
                if (attempt < llmOptions.retryCount - 1) {
                    await this.delay(llmOptions.retryDelay * (attempt + 1)); // Exponential backoff
                }
            }
        }

        // All retries failed, track the error
        this.llmMonitor.trackError(
            requestId,
            this.llmProvider.getDefaultModel(),
            lastError!,
            this.agentId
        );

        // Add final error to memory
        this.memoryManager.addToMemory('llm_error', {
            requestId,
            error: lastError!.message,
            totalAttempts: llmOptions.retryCount
        });

        throw lastError!;
    }

    /**
     * Generate completion with timeout
     * @param prompt Prompt to send
     * @param options LLM options
     * @returns Completion result
     */
    private async generateWithTimeout(prompt: string, options: LLMRequestOptions): Promise<any> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error(`LLM request timeout after ${options.timeout}ms`));
            }, options.timeout);

            this.llmProvider.generateCompletion(prompt, {
                temperature: options.temperature,
                maxTokens: options.maxTokens
            }).then(result => {
                clearTimeout(timeout);
                resolve(result);
            }).catch(error => {
                clearTimeout(timeout);
                reject(error);
            });
        });
    }

    /**
     * Delay utility for retry logic
     * @param ms Milliseconds to delay
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get the LLM provider
     * @returns LLM provider
     */
    getLLMProvider(): LanguageModelProvider {
        return this.llmProvider;
    }

    /**
     * Set the LLM provider
     * @param provider New LLM provider
     */
    setLLMProvider(provider: LanguageModelProvider): void {
        this.llmProvider = provider;

        // Add to memory
        this.memoryManager.addToMemory('provider_changed', {
            providerName: provider.getName(),
            timestamp: Date.now()
        });
    }

    /**
     * Get LLM statistics from memory
     * @returns LLM usage statistics
     */
    getLLMStats(): {
        totalRequests: number;
        totalResponses: number;
        totalErrors: number;
        averageLatency?: number;
        totalTokens?: number;
    } {
        const requests = this.memoryManager.getMemory('llm_request');
        const responses = this.memoryManager.getMemory('llm_response');
        const errors = this.memoryManager.getMemory('llm_error');

        let totalLatency = 0;
        let totalTokens = 0;
        let responseCount = 0;

        for (const response of responses) {
            if (response.content.latency) {
                totalLatency += response.content.latency;
                responseCount++;
            }
            if (response.content.tokens) {
                totalTokens += response.content.tokens;
            }
        }

        return {
            totalRequests: requests.length,
            totalResponses: responses.length,
            totalErrors: errors.length,
            averageLatency: responseCount > 0 ? totalLatency / responseCount : undefined,
            totalTokens: totalTokens > 0 ? totalTokens : undefined
        };
    }

    /**
     * Clear LLM-related memory
     */
    clearLLMMemory(): void {
        this.memoryManager.clearMemory('llm_request');
        this.memoryManager.clearMemory('llm_response');
        this.memoryManager.clearMemory('llm_error');
        this.memoryManager.clearMemory('llm_retry');
    }
}
