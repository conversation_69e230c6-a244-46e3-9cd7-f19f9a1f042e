/**
 * Agent Memory Manager
 *
 * Handles memory management for agents.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import { AgentMemoryItem, AgentOptions } from './types.js';

/**
 * Memory Manager for Agents
 * 
 * Manages agent memory storage, retrieval, and cleanup
 */
export class MemoryManager {
    private memory: AgentMemoryItem[] = [];
    private agentId: string;
    private options: AgentOptions;

    constructor(agentId: string, options: AgentOptions) {
        this.agentId = agentId;
        this.options = options;
    }

    /**
     * Add an item to agent memory
     * @param type Memory item type
     * @param content Memory item content
     * @param metadata Memory item metadata
     * @returns Memory item ID
     */
    addToMemory(type: string, content: any, metadata?: Record<string, any>): string {
        const id = `memory-${this.agentId}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        const memoryItem: AgentMemoryItem = {
            id,
            timestamp: Date.now(),
            type,
            content,
            metadata
        };

        this.memory.push(memoryItem);

        // Trim memory if it exceeds the maximum size
        if (this.memory.length > (this.options.maxMemoryItems || 100)) {
            this.memory.shift();
        }

        return id;
    }

    /**
     * Get memory items
     * @param type Optional memory item type filter
     * @param limit Optional limit on number of items to return
     * @returns Memory items
     */
    getMemory(type?: string, limit?: number): AgentMemoryItem[] {
        let items = [...this.memory];

        if (type) {
            items = items.filter(item => item.type === type);
        }

        // Sort by timestamp (newest first)
        items = items.sort((a, b) => b.timestamp - a.timestamp);

        if (limit) {
            items = items.slice(0, limit);
        }

        return items;
    }

    /**
     * Get memory item by ID
     * @param id Memory item ID
     * @returns Memory item or undefined
     */
    getMemoryItem(id: string): AgentMemoryItem | undefined {
        return this.memory.find(item => item.id === id);
    }

    /**
     * Update memory item
     * @param id Memory item ID
     * @param updates Updates to apply
     * @returns True if updated, false if not found
     */
    updateMemoryItem(id: string, updates: Partial<AgentMemoryItem>): boolean {
        const index = this.memory.findIndex(item => item.id === id);
        if (index === -1) {
            return false;
        }

        this.memory[index] = { ...this.memory[index], ...updates };
        return true;
    }

    /**
     * Remove memory item by ID
     * @param id Memory item ID
     * @returns True if removed, false if not found
     */
    removeMemoryItem(id: string): boolean {
        const index = this.memory.findIndex(item => item.id === id);
        if (index === -1) {
            return false;
        }

        this.memory.splice(index, 1);
        return true;
    }

    /**
     * Clear memory
     * @param type Optional memory item type filter
     */
    clearMemory(type?: string): void {
        if (type) {
            this.memory = this.memory.filter(item => item.type !== type);
        } else {
            this.memory = [];
        }
    }

    /**
     * Get memory statistics
     * @returns Memory statistics
     */
    getMemoryStats(): {
        totalItems: number;
        typeBreakdown: Record<string, number>;
        oldestItem?: AgentMemoryItem;
        newestItem?: AgentMemoryItem;
        memoryUsage: number;
    } {
        const typeBreakdown: Record<string, number> = {};
        let oldestItem: AgentMemoryItem | undefined;
        let newestItem: AgentMemoryItem | undefined;

        for (const item of this.memory) {
            typeBreakdown[item.type] = (typeBreakdown[item.type] || 0) + 1;

            if (!oldestItem || item.timestamp < oldestItem.timestamp) {
                oldestItem = item;
            }

            if (!newestItem || item.timestamp > newestItem.timestamp) {
                newestItem = item;
            }
        }

        return {
            totalItems: this.memory.length,
            typeBreakdown,
            oldestItem,
            newestItem,
            memoryUsage: this.memory.length / (this.options.maxMemoryItems || 100)
        };
    }

    /**
     * Search memory items
     * @param query Search query
     * @param type Optional type filter
     * @returns Matching memory items
     */
    searchMemory(query: string, type?: string): AgentMemoryItem[] {
        const searchTerm = query.toLowerCase();
        
        return this.memory.filter(item => {
            if (type && item.type !== type) {
                return false;
            }

            // Search in content (if it's a string or has string properties)
            const contentStr = typeof item.content === 'string' 
                ? item.content 
                : JSON.stringify(item.content);

            return contentStr.toLowerCase().includes(searchTerm) ||
                   item.type.toLowerCase().includes(searchTerm) ||
                   (item.metadata && JSON.stringify(item.metadata).toLowerCase().includes(searchTerm));
        });
    }

    /**
     * Get all memory items (for persistence)
     * @returns All memory items
     */
    getAllMemory(): AgentMemoryItem[] {
        return [...this.memory];
    }

    /**
     * Load memory items (from persistence)
     * @param items Memory items to load
     */
    loadMemory(items: AgentMemoryItem[]): void {
        this.memory = [...items];
        
        // Ensure we don't exceed the maximum
        if (this.memory.length > (this.options.maxMemoryItems || 100)) {
            this.memory = this.memory.slice(-this.options.maxMemoryItems!);
        }
    }
}
