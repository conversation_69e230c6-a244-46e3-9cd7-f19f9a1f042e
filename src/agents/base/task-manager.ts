/**
 * Agent Task Manager
 *
 * Handles task management for agents.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import { AgentTask, AgentStatus, AgentOptions } from './types.js';
import { TaskPriority } from '../../parallel/worker-pool.js';
import { MemoryManager } from './memory-manager.js';
import * as vscode from 'vscode';

/**
 * Task Manager for Agents
 * 
 * Manages task queuing, execution, and status tracking
 */
export class TaskManager {
    private tasks: Map<string, AgentTask> = new Map();
    private agentId: string;
    private options: AgentOptions;
    private memoryManager: MemoryManager;
    private eventEmitter: vscode.EventEmitter<any>;
    private executeCallback: (task: string, context: any) => Promise<any>;

    constructor(
        agentId: string,
        options: AgentOptions,
        memoryManager: MemoryManager,
        eventEmitter: vscode.EventEmitter<any>,
        executeCallback: (task: string, context: any) => Promise<any>
    ) {
        this.agentId = agentId;
        this.options = options;
        this.memoryManager = memoryManager;
        this.eventEmitter = eventEmitter;
        this.executeCallback = executeCallback;
    }

    /**
     * Queue a task for execution
     * @param task Task to execute
     * @param context Task context
     * @param priority Task priority
     * @returns Task ID
     */
    queueTask(task: string, context: any = {}, priority: TaskPriority = TaskPriority.NORMAL): string {
        const taskId = `task-${this.agentId}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        const agentTask: AgentTask = {
            id: taskId,
            task,
            context,
            priority,
            timestamp: Date.now(),
            status: 'pending'
        };

        this.tasks.set(taskId, agentTask);

        // Add to memory
        this.memoryManager.addToMemory('task_queued', {
            taskId,
            task,
            priority,
            timestamp: Date.now()
        });

        return taskId;
    }

    /**
     * Process the task queue
     * @param currentStatus Current agent status
     * @returns New agent status
     */
    async processTaskQueue(currentStatus: AgentStatus): Promise<{ status: AgentStatus; result?: any }> {
        if (currentStatus === AgentStatus.BUSY || currentStatus === AgentStatus.DISPOSED) {
            return { status: currentStatus };
        }

        // Find the highest priority pending task
        const highestPriorityTask = this.getHighestPriorityTask();

        if (!highestPriorityTask) {
            return { status: AgentStatus.IDLE };
        }

        // Execute the task
        highestPriorityTask.status = 'running';

        try {
            const result = await this.executeCallback(highestPriorityTask.task, highestPriorityTask.context);

            highestPriorityTask.status = 'completed';
            highestPriorityTask.result = result;

            // Add to memory
            this.memoryManager.addToMemory('task_result', {
                taskId: highestPriorityTask.id,
                task: highestPriorityTask.task,
                result
            });

            // Emit event
            this.eventEmitter.fire({
                type: 'task_completed',
                taskId: highestPriorityTask.id,
                result
            });

            return { status: AgentStatus.IDLE, result };
        } catch (error) {
            highestPriorityTask.status = 'failed';
            highestPriorityTask.error = error as Error;

            // Add to memory
            this.memoryManager.addToMemory('task_error', {
                taskId: highestPriorityTask.id,
                task: highestPriorityTask.task,
                error
            });

            // Emit event
            this.eventEmitter.fire({
                type: 'task_failed',
                taskId: highestPriorityTask.id,
                error
            });

            return { status: AgentStatus.ERROR };
        }
    }

    /**
     * Get the highest priority pending task
     * @returns Highest priority task or undefined
     */
    private getHighestPriorityTask(): AgentTask | undefined {
        let highestPriorityTask: AgentTask | undefined;
        let highestPriority = -1;

        for (const task of this.tasks.values()) {
            if (task.status === 'pending' && task.priority > highestPriority) {
                highestPriorityTask = task;
                highestPriority = task.priority;
            }
        }

        return highestPriorityTask;
    }

    /**
     * Get task status
     * @param taskId Task ID
     * @returns Task status or undefined if not found
     */
    getTaskStatus(taskId: string): AgentTask | undefined {
        return this.tasks.get(taskId);
    }

    /**
     * Get all tasks
     * @param status Optional status filter
     * @returns Array of tasks
     */
    getAllTasks(status?: AgentTask['status']): AgentTask[] {
        const tasks = Array.from(this.tasks.values());
        
        if (status) {
            return tasks.filter(task => task.status === status);
        }
        
        return tasks;
    }

    /**
     * Cancel a task
     * @param taskId Task ID
     * @returns True if cancelled, false if not found or already completed
     */
    cancelTask(taskId: string): boolean {
        const task = this.tasks.get(taskId);
        
        if (!task || task.status === 'completed' || task.status === 'failed') {
            return false;
        }

        if (task.status === 'running') {
            // Cannot cancel running tasks
            return false;
        }

        task.status = 'failed';
        task.error = new Error('Task cancelled');

        // Add to memory
        this.memoryManager.addToMemory('task_cancelled', {
            taskId,
            task: task.task,
            timestamp: Date.now()
        });

        return true;
    }

    /**
     * Clear completed tasks
     * @param olderThan Optional timestamp to clear tasks older than
     */
    clearCompletedTasks(olderThan?: number): number {
        let clearedCount = 0;
        const cutoffTime = olderThan || 0;

        for (const [taskId, task] of this.tasks.entries()) {
            if ((task.status === 'completed' || task.status === 'failed') && 
                task.timestamp < cutoffTime) {
                this.tasks.delete(taskId);
                clearedCount++;
            }
        }

        return clearedCount;
    }

    /**
     * Get task statistics
     * @returns Task statistics
     */
    getTaskStats(): {
        total: number;
        pending: number;
        running: number;
        completed: number;
        failed: number;
        averageExecutionTime?: number;
    } {
        const tasks = Array.from(this.tasks.values());
        const stats = {
            total: tasks.length,
            pending: 0,
            running: 0,
            completed: 0,
            failed: 0,
            averageExecutionTime: undefined as number | undefined
        };

        let totalExecutionTime = 0;
        let completedTasksWithTime = 0;

        for (const task of tasks) {
            stats[task.status]++;

            if (task.status === 'completed' && task.result) {
                // Calculate execution time if available
                const executionTime = Date.now() - task.timestamp;
                totalExecutionTime += executionTime;
                completedTasksWithTime++;
            }
        }

        if (completedTasksWithTime > 0) {
            stats.averageExecutionTime = totalExecutionTime / completedTasksWithTime;
        }

        return stats;
    }

    /**
     * Clear all tasks
     */
    clearAllTasks(): void {
        this.tasks.clear();
    }
}
