/**
 * Base Agent Types
 *
 * Type definitions for the base agent system.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import { TaskPriority } from '../../parallel/worker-pool.js';

/**
 * Agent Status
 */
export enum AgentStatus {
    IDLE = 'idle',
    BUSY = 'busy',
    ERROR = 'error',
    DISPOSED = 'disposed'
}

/**
 * Agent Memory Item
 */
export interface AgentMemoryItem {
    id: string;
    timestamp: number;
    type: string;
    content: any;
    metadata?: Record<string, any>;
}

/**
 * Agent Task
 */
export interface AgentTask {
    id: string;
    task: string;
    context: any;
    priority: TaskPriority;
    timestamp: number;
    status: 'pending' | 'running' | 'completed' | 'failed';
    result?: any;
    error?: Error;
}

/**
 * Agent Options
 */
export interface AgentOptions {
    maxMemoryItems?: number;
    contextWindowSize?: number;
    maxTokens?: number;
    temperature?: number;
    useCache?: boolean;
    cacheExpiration?: number;
    debugMode?: boolean;
    timeout?: number;
    retryCount?: number;
    retryDelay?: number;
    [key: string]: any;
}

/**
 * Agent Message
 */
export interface AgentMessage {
    senderId: string;
    recipientId: string;
    message: any;
    metadata?: Record<string, any>;
    timestamp: number;
}

/**
 * Agent Event
 */
export interface AgentEvent {
    type: string;
    agentId: string;
    timestamp: number;
    data: any;
}

/**
 * LLM Request Options
 */
export interface LLMRequestOptions {
    temperature?: number;
    maxTokens?: number;
    timeout?: number;
    retryCount?: number;
    retryDelay?: number;
    [key: string]: any;
}
