/**
 * Code Analysis Agent for X10sion
 *
 * This module implements a specialized agent for analyzing code, identifying issues,
 * and suggesting improvements. It can analyze code quality, security vulnerabilities,
 * performance issues, and more.
 *
 * Based on May 2025 best practices for code analysis.
 * Refactored into modular components for better maintainability.
 */

import * as vscode from 'vscode';
import { BaseAgent, AgentOptions } from './base-agent.js';
import { WorkerPool, TaskPriority } from '../parallel/worker-pool.js';
import { LLMMonitor } from '../monitoring/llm-monitor.js';
import { LanguageModelProvider } from '../llm/providers/base-provider.js';

// Import modular components
import {
    IssueSeverity,
    IssueCategory,
    CodeIssue,
    CodeMetric,
    CodeAnalysisResult,
    CodeAnalysisRequest,
    CodeAnalysisAgentOptions
} from './code-analysis/types.js';
import { CodeParser } from './code-analysis/code-parsing.js';
import { IssueDetector } from './code-analysis/issue-detection.js';
import { SeverityFilter } from './code-analysis/severity-filtering.js';
import { ResultFormatter } from './code-analysis/result-formatting.js';

// Re-export types for external use
export {
    IssueSeverity,
    IssueCategory,
    CodeIssue,
    CodeMetric,
    CodeAnalysisResult,
    CodeAnalysisRequest,
    CodeAnalysisAgentOptions
} from './code-analysis/types.js';

/**
 * Code Analysis Agent Implementation
 */
export class CodeAnalysisAgent extends BaseAgent {
    private agentOptions: CodeAnalysisAgentOptions;
    private codeParser: CodeParser;
    private issueDetector: IssueDetector;
    private severityFilter: SeverityFilter;
    private resultFormatter: ResultFormatter;

    constructor(
        id: string,
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor,
        llmProvider: LanguageModelProvider,
        options: CodeAnalysisAgentOptions = {}
    ) {
        super(
            id,
            'Code Analysis Agent',
            'Analyzes code for issues and suggests improvements',
            workerPool,
            llmMonitor,
            llmProvider,
            options
        );

        // Set default options
        this.agentOptions = {
            defaultCategories: Object.values(IssueCategory),
            defaultMinSeverity: IssueSeverity.INFO,
            includeMetricsByDefault: true,
            includeRecommendationsByDefault: true,
            maxIssues: 20,
            ...options
        };

        // Initialize modular components
        this.codeParser = new CodeParser(this.agentOptions);
        this.issueDetector = new IssueDetector(this.getLLMProvider());
        this.severityFilter = new SeverityFilter(this.agentOptions);
        this.resultFormatter = new ResultFormatter(this.workerPool, this.agentOptions.maxIssues);
    }

    /**
     * Execute a task with the agent
     */
    async execute(task: string, context: any = {}): Promise<CodeAnalysisResult> {
        try {
            // Parse the task as a code analysis request
            const request = this.codeParser.parseRequest(task, context);

            // Validate the request
            const validation = this.codeParser.validateRequest(request);
            if (!validation.valid) {
                throw new Error(`Invalid request: ${validation.errors.join(', ')}`);
            }

            // Normalize the request
            const normalizedRequest = this.codeParser.normalizeRequest(request);

            // Create a prompt for code analysis
            const prompt = this.issueDetector.createAnalysisPrompt(normalizedRequest);

            // Generate analysis using the LLM
            const analysisText = await this.issueDetector.generateAnalysis(prompt);

            // Parse the analysis result
            const analysisResult = await this.resultFormatter.parseAnalysisResult(analysisText, normalizedRequest);

            // Apply additional filters
            const filteredResult = this.severityFilter.applyFilters(analysisResult, normalizedRequest);

            return filteredResult;
        } catch (error) {
            console.error('Error in CodeAnalysisAgent.execute:', error);
            throw error;
        }
    }

    /**
     * Update LLM provider in all components
     */
    setLLMProvider(provider: LanguageModelProvider): void {
        super.setLLMProvider(provider);
        this.issueDetector.setLLMProvider(provider);
    }

    /**
     * Get issue statistics for the last analysis
     */
    getIssueStatistics(issues: CodeIssue[]) {
        return this.severityFilter.getIssueStatistics(issues);
    }

    /**
     * Format analysis result for display
     */
    formatResult(result: CodeAnalysisResult): string {
        return this.resultFormatter.formatForDisplay(result);
    }

    /**
     * Create a summary report for multiple analysis results
     */
    createSummaryReport(results: Record<string, CodeAnalysisResult>): string {
        return this.resultFormatter.createSummaryReport(results);
    }

    /**
     * Analyze a file in the workspace
     */
    async analyzeFile(filePath: string, options: Partial<CodeAnalysisRequest> = {}): Promise<CodeAnalysisResult> {
        try {
            // Read the file
            const document = await vscode.workspace.openTextDocument(filePath);
            const code = document.getText();
            const language = document.languageId;

            // Create a request
            const request: CodeAnalysisRequest = {
                code,
                language,
                filePath,
                ...options
            };

            // Execute the analysis
            return await this.execute(`Analyze file: ${filePath}`, request);
        } catch (error) {
            console.error(`Error analyzing file ${filePath}:`, error);
            throw error;
        }
    }

    /**
     * Analyze multiple files in the workspace
     */
    async analyzeFiles(
        filePaths: string[],
        options: Partial<CodeAnalysisRequest> = {}
    ): Promise<Record<string, CodeAnalysisResult>> {
        const results: Record<string, CodeAnalysisResult> = {};

        // Analyze each file in parallel
        await Promise.all(
            filePaths.map(async (filePath) => {
                try {
                    results[filePath] = await this.analyzeFile(filePath, options);
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    console.error(`Error analyzing file ${filePath}:`, errorMessage);
                    // Add a placeholder result for failed analyses
                    results[filePath] = {
                        issues: [],
                        metrics: [],
                        summary: `Failed to analyze file: ${errorMessage}`,
                        recommendations: [],
                        codeQualityScore: 0
                    };
                }
            })
        );

        return results;
    }
}
