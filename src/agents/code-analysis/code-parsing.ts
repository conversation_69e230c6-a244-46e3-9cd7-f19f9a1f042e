/**
 * Code Parsing Module for Code Analysis Agent
 *
 * This module handles parsing of code requests, extracting code blocks,
 * language detection, and request normalization.
 *
 * Based on May 2025 best practices for code parsing.
 */

import * as vscode from 'vscode';
import {
    CodeAnalysisRequest,
    CodeAnalysisAgentOptions,
    CodeBlock,
    IssueCategory,
    IssueSeverity,
    LANGUAGE_PATTERNS
} from './types.js';

/**
 * Code Parser class for handling code analysis requests
 */
export class CodeParser {
    private agentOptions: CodeAnalysisAgentOptions;

    constructor(agentOptions: CodeAnalysisAgentOptions) {
        this.agentOptions = agentOptions;
    }

    /**
     * Parse the task into a code analysis request
     */
    parseRequest(task: string, context: any): CodeAnalysisRequest {
        // Check if the task is already a code analysis request
        if (typeof task === 'object' && 'code' in task && 'language' in task) {
            return task as unknown as CodeAnalysisRequest;
        }

        // Start with defaults
        const request: CodeAnalysisRequest = {
            code: '',
            language: '',
            categories: this.agentOptions.defaultCategories,
            minSeverity: this.agentOptions.defaultMinSeverity,
            includeMetrics: this.agentOptions.includeMetricsByDefault,
            includeRecommendations: this.agentOptions.includeRecommendationsByDefault
        };

        // If task is a string, try to extract code and language
        if (typeof task === 'string') {
            // Check if the task contains code blocks
            const codeBlocks = this.extractCodeBlocks(task);
            if (codeBlocks.length > 0) {
                request.code = codeBlocks[0].code;
                request.language = codeBlocks[0].language || this.inferLanguage(task);
            } else {
                // Assume the task is a file path or a request to analyze code
                request.code = context.code || '';
                request.language = context.language || this.inferLanguage(task);
                request.filePath = task.match(/^[\w\/\.-]+\.\w+$/) ? task : context.filePath;
            }
        }

        // Override with context if provided
        this.applyContextOverrides(request, context);

        // If we still don't have code, try to read from the file path
        if (!request.code && request.filePath) {
            this.loadCodeFromFile(request);
        }

        return request;
    }

    /**
     * Extract code blocks from text
     */
    extractCodeBlocks(text: string): CodeBlock[] {
        const codeBlockRegex = /```(\w*)\n([\s\S]*?)```/g;
        const codeBlocks: CodeBlock[] = [];

        let match;
        while ((match = codeBlockRegex.exec(text)) !== null) {
            codeBlocks.push({
                language: match[1].toLowerCase(),
                code: match[2].trim()
            });
        }

        return codeBlocks;
    }

    /**
     * Infer the programming language from text
     */
    inferLanguage(text: string): string {
        const textLower = text.toLowerCase();

        // Check for explicit language mentions
        for (const [language, pattern] of Object.entries(LANGUAGE_PATTERNS)) {
            // Check keywords
            for (const keyword of pattern.keywords) {
                if (textLower.includes(keyword)) {
                    return language;
                }
            }

            // Check file extension patterns
            for (const extension of pattern.extensions) {
                if (textLower.includes(extension)) {
                    return language;
                }
            }

            // Check code patterns
            for (const regex of pattern.patterns) {
                if (regex.test(text)) {
                    return language;
                }
            }
        }

        // Default to TypeScript for this extension
        return 'typescript';
    }

    /**
     * Apply context overrides to the request
     */
    private applyContextOverrides(request: CodeAnalysisRequest, context: any): void {
        if (context.code) {
            request.code = context.code;
        }

        if (context.language) {
            request.language = context.language;
        }

        if (context.filePath) {
            request.filePath = context.filePath;
        }

        if (context.categories) {
            request.categories = context.categories;
        }

        if (context.minSeverity) {
            request.minSeverity = context.minSeverity;
        }

        if (context.includeMetrics !== undefined) {
            request.includeMetrics = context.includeMetrics;
        }

        if (context.includeRecommendations !== undefined) {
            request.includeRecommendations = context.includeRecommendations;
        }

        if (context.projectConventions) {
            request.projectConventions = context.projectConventions;
        }
    }

    /**
     * Load code from file if available
     */
    private loadCodeFromFile(request: CodeAnalysisRequest): void {
        if (!request.filePath) {
            return;
        }

        try {
            const document = vscode.workspace.textDocuments.find(
                doc => doc.uri.fsPath === request.filePath
            );

            if (document) {
                request.code = document.getText();
                request.language = document.languageId;
            }
        } catch (error) {
            console.warn(`Failed to read file: ${request.filePath}`, error);
        }
    }

    /**
     * Validate the parsed request
     */
    validateRequest(request: CodeAnalysisRequest): { valid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!request.code || request.code.trim().length === 0) {
            errors.push('Code content is required for analysis');
        }

        if (!request.language || request.language.trim().length === 0) {
            errors.push('Programming language must be specified');
        }

        if (request.categories && request.categories.length === 0) {
            errors.push('At least one analysis category must be specified');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Normalize the request with default values
     */
    normalizeRequest(request: CodeAnalysisRequest): CodeAnalysisRequest {
        return {
            ...request,
            categories: request.categories || this.agentOptions.defaultCategories || Object.values(IssueCategory),
            minSeverity: request.minSeverity || this.agentOptions.defaultMinSeverity || IssueSeverity.INFO,
            includeMetrics: request.includeMetrics ?? this.agentOptions.includeMetricsByDefault ?? true,
            includeRecommendations: request.includeRecommendations ?? this.agentOptions.includeRecommendationsByDefault ?? true
        };
    }
}
