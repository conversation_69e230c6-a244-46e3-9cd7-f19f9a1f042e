/**
 * Issue Detection Module for Code Analysis Agent
 *
 * This module handles the creation of analysis prompts and communication
 * with the LLM for issue detection and code analysis.
 *
 * Based on May 2025 best practices for LLM integration.
 */

import { LanguageModelProvider } from '../../llm/providers/base-provider.js';
import { CodeAnalysisRequest } from './types.js';

/**
 * Issue Detector class for generating analysis prompts and LLM communication
 */
export class IssueDetector {
    private llmProvider: LanguageModelProvider;

    constructor(llmProvider: LanguageModelProvider) {
        this.llmProvider = llmProvider;
    }

    /**
     * Update the LLM provider
     */
    setLLMProvider(provider: LanguageModelProvider): void {
        this.llmProvider = provider;
    }

    /**
     * Create a prompt for code analysis
     */
    createAnalysisPrompt(request: CodeAnalysisRequest): string {
        let prompt = `Analyze the following ${request.language} code for issues and provide detailed feedback:

\`\`\`${request.language}
${request.code}
\`\`\`

Analyze the code for the following categories:
${request.categories?.map(category => `- ${category}`).join('\n')}

Minimum severity level: ${request.minSeverity}

${request.includeMetrics ? 'Include metrics for code quality, complexity, and maintainability.' : ''}
${request.includeRecommendations ? 'Provide specific recommendations for improving the code.' : ''}
${request.projectConventions ? `Consider the following project conventions:\n${request.projectConventions}` : ''}

For each issue found, provide:
1. A title
2. A description
3. The severity (info, warning, error, critical)
4. The category
5. The line number (if applicable)
6. A code snippet showing the issue
7. A suggestion for fixing the issue

Also provide:
1. A summary of the analysis
2. A code quality score (0-100)
3. Key recommendations for improvement

Format the response as JSON with the following structure:
{
  "issues": [
    {
      "id": "unique-id",
      "title": "Issue title",
      "description": "Detailed description",
      "severity": "severity-level",
      "category": "issue-category",
      "lineNumber": 123,
      "codeSnippet": "code with issue",
      "suggestion": "how to fix"
    }
  ],
  "metrics": [
    {
      "name": "metric-name",
      "value": 123,
      "description": "metric description",
      "threshold": 100,
      "status": "good|warning|bad"
    }
  ],
  "summary": "Overall analysis summary",
  "recommendations": [
    "recommendation 1",
    "recommendation 2"
  ],
  "codeQualityScore": 85
}`;

        return prompt;
    }

    /**
     * Generate analysis using the LLM
     */
    async generateAnalysis(prompt: string): Promise<string> {
        const completion = await this.llmProvider.generateCompletion(prompt, {
            maxTokens: 2000,
            temperature: 0.1, // Lower temperature for more deterministic analysis
            stopSequences: []
        });

        return completion.text;
    }

    /**
     * Create a focused prompt for specific issue categories
     */
    createFocusedPrompt(request: CodeAnalysisRequest, focusCategory: string): string {
        let prompt = `Perform a focused ${focusCategory} analysis of the following ${request.language} code:

\`\`\`${request.language}
${request.code}
\`\`\`

Focus specifically on ${focusCategory} issues. Look for:`;

        // Add category-specific guidance
        switch (focusCategory.toLowerCase()) {
            case 'security':
                prompt += `
- SQL injection vulnerabilities
- Cross-site scripting (XSS) risks
- Authentication and authorization issues
- Input validation problems
- Cryptographic weaknesses
- Sensitive data exposure`;
                break;
            case 'performance':
                prompt += `
- Inefficient algorithms or data structures
- Memory leaks or excessive memory usage
- Unnecessary computations or loops
- Database query optimization opportunities
- Caching opportunities
- Resource management issues`;
                break;
            case 'quality':
                prompt += `
- Code complexity and readability
- Naming conventions
- Code duplication
- Function and class size
- Proper error handling
- Documentation quality`;
                break;
            case 'maintainability':
                prompt += `
- Code organization and structure
- Separation of concerns
- Dependency management
- Testing coverage
- Configuration management
- Code reusability`;
                break;
            default:
                prompt += `
- Best practices for ${focusCategory}
- Common anti-patterns
- Industry standards compliance
- Code quality indicators`;
        }

        prompt += `

Provide detailed findings in JSON format with the same structure as before, but focus only on ${focusCategory}-related issues.`;

        return prompt;
    }

    /**
     * Create a prompt for quick analysis (lighter version)
     */
    createQuickAnalysisPrompt(request: CodeAnalysisRequest): string {
        return `Perform a quick analysis of this ${request.language} code and identify the top 3 most critical issues:

\`\`\`${request.language}
${request.code}
\`\`\`

Focus on:
- Critical errors that would prevent the code from working
- Security vulnerabilities
- Performance bottlenecks

Provide a concise JSON response with only the most important findings.`;
    }

    /**
     * Validate LLM response format
     */
    validateLLMResponse(response: string): { valid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!response || response.trim().length === 0) {
            errors.push('Empty response from LLM');
            return { valid: false, errors };
        }

        // Check for JSON structure
        const jsonMatch = response.match(/```json\n([\s\S]*?)```/) ||
                         response.match(/```\n([\s\S]*?)```/) ||
                         response.match(/{[\s\S]*}/);

        if (!jsonMatch) {
            errors.push('No JSON structure found in LLM response');
        }

        try {
            const jsonStr = jsonMatch ? jsonMatch[0].replace(/```json\n|```\n|```/g, '').trim() : response;
            const parsed = JSON.parse(jsonStr);

            if (!parsed.issues && !parsed.summary) {
                errors.push('Response missing required fields (issues or summary)');
            }
        } catch (error) {
            errors.push(`Invalid JSON format: ${error instanceof Error ? error.message : String(error)}`);
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Extract and clean JSON from LLM response
     */
    extractJSON(response: string): string {
        // Extract JSON from the response
        const jsonMatch = response.match(/```json\n([\s\S]*?)```/) ||
                         response.match(/```\n([\s\S]*?)```/) ||
                         response.match(/{[\s\S]*}/);

        let jsonStr = jsonMatch ? jsonMatch[0] : response;

        // Clean up the JSON string
        jsonStr = jsonStr.replace(/```json\n|```\n|```/g, '').trim();

        return jsonStr;
    }
}
