/**
 * Result Formatting Module for Code Analysis Agent
 *
 * This module handles parsing and formatting of LLM responses into
 * structured analysis results.
 *
 * Based on May 2025 best practices for data processing.
 */

import { WorkerPool, TaskPriority, Task } from '../../parallel/worker-pool.js';
import {
    CodeAnalysisResult,
    CodeAnalysisRequest,
    IssueSeverity,
    SEVERITY_ORDER
} from './types.js';

/**
 * Create a task for the worker pool
 */
function createTask<T>(fn: () => T | Promise<T>, priority: TaskPriority = TaskPriority.NORMAL): Task<T> {
    return {
        execute: async () => {
            const result = fn();
            return result instanceof Promise ? await result : result;
        },
        priority
    };
}

/**
 * Result Formatter class for processing LLM responses
 */
export class ResultFormatter {
    private workerPool: WorkerPool;
    private maxIssues?: number;

    constructor(workerPool: WorkerPool, maxIssues?: number) {
        this.workerPool = workerPool;
        this.maxIssues = maxIssues;
    }

    /**
     * Parse the analysis result from the LLM response
     */
    async parseAnalysisResult(
        analysisText: string,
        request: CodeAnalysisRequest
    ): Promise<CodeAnalysisResult> {
        // Use worker pool for parallel processing
        return await this.workerPool.execute(createTask(() => {
            try {
                // Extract JSON from the response
                const jsonStr = this.extractJSON(analysisText);

                // Parse the JSON
                const result = JSON.parse(jsonStr) as CodeAnalysisResult;

                // Ensure all required properties exist
                const normalizedResult = this.normalizeResult(result);

                // Apply filters and limits
                const filteredResult = this.applyFilters(normalizedResult, request);

                return filteredResult;
            } catch (error) {
                console.error('Error parsing analysis result:', error);

                // Return a fallback result
                return this.createFallbackResult(error);
            }
        }, TaskPriority.NORMAL));
    }

    /**
     * Extract JSON from LLM response
     */
    private extractJSON(analysisText: string): string {
        const jsonMatch = analysisText.match(/```json\n([\s\S]*?)```/) ||
                         analysisText.match(/```\n([\s\S]*?)```/) ||
                         analysisText.match(/{[\s\S]*}/);

        let jsonStr = jsonMatch ? jsonMatch[0] : analysisText;

        // Clean up the JSON string
        jsonStr = jsonStr.replace(/```json\n|```\n|```/g, '').trim();

        return jsonStr;
    }

    /**
     * Normalize the result to ensure all required properties exist
     */
    private normalizeResult(result: any): CodeAnalysisResult {
        return {
            issues: Array.isArray(result.issues) ? result.issues : [],
            metrics: Array.isArray(result.metrics) ? result.metrics : [],
            summary: typeof result.summary === 'string' ? result.summary : 'No summary provided',
            recommendations: Array.isArray(result.recommendations) ? result.recommendations : [],
            codeQualityScore: typeof result.codeQualityScore === 'number' ? 
                Math.max(0, Math.min(100, result.codeQualityScore)) : 0
        };
    }

    /**
     * Apply filters and limits to the result
     */
    private applyFilters(result: CodeAnalysisResult, request: CodeAnalysisRequest): CodeAnalysisResult {
        let filteredIssues = [...result.issues];

        // Filter issues by minimum severity if needed
        if (request.minSeverity) {
            const minSeverityLevel = SEVERITY_ORDER[request.minSeverity];

            filteredIssues = filteredIssues.filter(issue =>
                SEVERITY_ORDER[issue.severity as IssueSeverity] >= minSeverityLevel
            );
        }

        // Limit the number of issues if needed
        if (this.maxIssues && filteredIssues.length > this.maxIssues) {
            // Sort by severity (highest first) and take the top issues
            filteredIssues.sort((a, b) => {
                const severityA = SEVERITY_ORDER[a.severity as IssueSeverity];
                const severityB = SEVERITY_ORDER[b.severity as IssueSeverity];
                return severityB - severityA;
            });
            filteredIssues = filteredIssues.slice(0, this.maxIssues);
        }

        return {
            ...result,
            issues: filteredIssues
        };
    }

    /**
     * Create a fallback result for when parsing fails
     */
    private createFallbackResult(error: any): CodeAnalysisResult {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
            issues: [],
            metrics: [],
            summary: `Failed to parse analysis result: ${errorMessage}`,
            recommendations: ['Try analyzing the code again with a different approach'],
            codeQualityScore: 0
        };
    }

    /**
     * Format result for display
     */
    formatForDisplay(result: CodeAnalysisResult): string {
        let output = `# Code Analysis Result\n\n`;

        // Summary
        output += `## Summary\n${result.summary}\n\n`;

        // Quality Score
        output += `## Code Quality Score: ${result.codeQualityScore}/100\n\n`;

        // Issues
        if (result.issues.length > 0) {
            output += `## Issues Found (${result.issues.length})\n\n`;
            
            for (const issue of result.issues) {
                output += `### ${issue.title} (${issue.severity.toUpperCase()})\n`;
                output += `**Category:** ${issue.category}\n`;
                output += `**Description:** ${issue.description}\n`;
                
                if (issue.lineNumber) {
                    output += `**Line:** ${issue.lineNumber}\n`;
                }
                
                if (issue.codeSnippet) {
                    output += `**Code:**\n\`\`\`\n${issue.codeSnippet}\n\`\`\`\n`;
                }
                
                if (issue.suggestion) {
                    output += `**Suggestion:** ${issue.suggestion}\n`;
                }
                
                output += '\n';
            }
        } else {
            output += `## No Issues Found\n\n`;
        }

        // Metrics
        if (result.metrics.length > 0) {
            output += `## Metrics\n\n`;
            
            for (const metric of result.metrics) {
                output += `- **${metric.name}:** ${metric.value}`;
                if (metric.threshold) {
                    output += ` (threshold: ${metric.threshold})`;
                }
                output += ` - ${metric.status.toUpperCase()}\n`;
                output += `  ${metric.description}\n`;
            }
            output += '\n';
        }

        // Recommendations
        if (result.recommendations.length > 0) {
            output += `## Recommendations\n\n`;
            
            for (let i = 0; i < result.recommendations.length; i++) {
                output += `${i + 1}. ${result.recommendations[i]}\n`;
            }
        }

        return output;
    }

    /**
     * Format result as JSON string
     */
    formatAsJSON(result: CodeAnalysisResult, pretty: boolean = true): string {
        return JSON.stringify(result, null, pretty ? 2 : 0);
    }

    /**
     * Create a summary report
     */
    createSummaryReport(results: Record<string, CodeAnalysisResult>): string {
        const fileCount = Object.keys(results).length;
        let totalIssues = 0;
        let totalCritical = 0;
        let totalErrors = 0;
        let totalWarnings = 0;
        let averageQuality = 0;

        for (const result of Object.values(results)) {
            totalIssues += result.issues.length;
            averageQuality += result.codeQualityScore;
            
            for (const issue of result.issues) {
                switch (issue.severity) {
                    case IssueSeverity.CRITICAL:
                        totalCritical++;
                        break;
                    case IssueSeverity.ERROR:
                        totalErrors++;
                        break;
                    case IssueSeverity.WARNING:
                        totalWarnings++;
                        break;
                }
            }
        }

        averageQuality = fileCount > 0 ? Math.round(averageQuality / fileCount) : 0;

        return `# Code Analysis Summary Report

**Files Analyzed:** ${fileCount}
**Total Issues:** ${totalIssues}
**Average Quality Score:** ${averageQuality}/100

## Issue Breakdown
- **Critical:** ${totalCritical}
- **Errors:** ${totalErrors}
- **Warnings:** ${totalWarnings}
- **Info:** ${totalIssues - totalCritical - totalErrors - totalWarnings}

## Files with Issues
${Object.entries(results)
    .filter(([_, result]) => result.issues.length > 0)
    .map(([file, result]) => `- ${file}: ${result.issues.length} issues (Quality: ${result.codeQualityScore}/100)`)
    .join('\n')}
`;
    }
}
