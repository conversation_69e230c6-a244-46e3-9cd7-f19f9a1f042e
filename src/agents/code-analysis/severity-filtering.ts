/**
 * Severity Filtering Module for Code Analysis Agent
 *
 * This module handles filtering and prioritization of issues based on
 * severity levels, categories, and other criteria.
 *
 * Based on May 2025 best practices for issue management.
 */

import {
    CodeIssue,
    CodeAnalysisResult,
    CodeAnalysisRequest,
    IssueSeverity,
    IssueCategory,
    SEVERITY_ORDER,
    CodeAnalysisAgentOptions
} from './types.js';

/**
 * Severity Filter class for managing issue filtering and prioritization
 */
export class SeverityFilter {
    private agentOptions: CodeAnalysisAgentOptions;

    constructor(agentOptions: CodeAnalysisAgentOptions) {
        this.agentOptions = agentOptions;
    }

    /**
     * Filter issues by minimum severity level
     */
    filterBySeverity(issues: CodeIssue[], minSeverity: IssueSeverity): CodeIssue[] {
        const minSeverityLevel = SEVERITY_ORDER[minSeverity];

        return issues.filter(issue =>
            SEVERITY_ORDER[issue.severity as IssueSeverity] >= minSeverityLevel
        );
    }

    /**
     * Filter issues by categories
     */
    filterByCategories(issues: CodeIssue[], categories: IssueCategory[]): CodeIssue[] {
        if (!categories || categories.length === 0) {
            return issues;
        }

        return issues.filter(issue =>
            categories.includes(issue.category as IssueCategory)
        );
    }

    /**
     * Limit the number of issues
     */
    limitIssues(issues: CodeIssue[], maxIssues?: number): CodeIssue[] {
        const limit = maxIssues || this.agentOptions.maxIssues;
        
        if (!limit || issues.length <= limit) {
            return issues;
        }

        // Sort by severity (highest first) and take the top issues
        const sortedIssues = this.sortBySeverity(issues);
        return sortedIssues.slice(0, limit);
    }

    /**
     * Sort issues by severity (highest to lowest)
     */
    sortBySeverity(issues: CodeIssue[]): CodeIssue[] {
        return [...issues].sort((a, b) => {
            const severityA = SEVERITY_ORDER[a.severity as IssueSeverity];
            const severityB = SEVERITY_ORDER[b.severity as IssueSeverity];
            return severityB - severityA; // Descending order (highest severity first)
        });
    }

    /**
     * Sort issues by category
     */
    sortByCategory(issues: CodeIssue[]): CodeIssue[] {
        const categoryOrder = {
            [IssueCategory.SECURITY]: 0,
            [IssueCategory.QUALITY]: 1,
            [IssueCategory.PERFORMANCE]: 2,
            [IssueCategory.MAINTAINABILITY]: 3,
            [IssueCategory.BEST_PRACTICE]: 4,
            [IssueCategory.COMPATIBILITY]: 5,
            [IssueCategory.ACCESSIBILITY]: 6
        };

        return [...issues].sort((a, b) => {
            const categoryA = categoryOrder[a.category as IssueCategory] ?? 999;
            const categoryB = categoryOrder[b.category as IssueCategory] ?? 999;
            return categoryA - categoryB;
        });
    }

    /**
     * Group issues by severity
     */
    groupBySeverity(issues: CodeIssue[]): Record<IssueSeverity, CodeIssue[]> {
        const groups: Record<IssueSeverity, CodeIssue[]> = {
            [IssueSeverity.CRITICAL]: [],
            [IssueSeverity.ERROR]: [],
            [IssueSeverity.WARNING]: [],
            [IssueSeverity.INFO]: []
        };

        for (const issue of issues) {
            const severity = issue.severity as IssueSeverity;
            if (groups[severity]) {
                groups[severity].push(issue);
            }
        }

        return groups;
    }

    /**
     * Group issues by category
     */
    groupByCategory(issues: CodeIssue[]): Record<IssueCategory, CodeIssue[]> {
        const groups: Record<IssueCategory, CodeIssue[]> = {
            [IssueCategory.SECURITY]: [],
            [IssueCategory.QUALITY]: [],
            [IssueCategory.PERFORMANCE]: [],
            [IssueCategory.MAINTAINABILITY]: [],
            [IssueCategory.BEST_PRACTICE]: [],
            [IssueCategory.COMPATIBILITY]: [],
            [IssueCategory.ACCESSIBILITY]: []
        };

        for (const issue of issues) {
            const category = issue.category as IssueCategory;
            if (groups[category]) {
                groups[category].push(issue);
            }
        }

        return groups;
    }

    /**
     * Apply all filters to the analysis result
     */
    applyFilters(result: CodeAnalysisResult, request: CodeAnalysisRequest): CodeAnalysisResult {
        let filteredIssues = [...result.issues];

        // Filter by minimum severity if specified
        if (request.minSeverity) {
            filteredIssues = this.filterBySeverity(filteredIssues, request.minSeverity);
        }

        // Filter by categories if specified
        if (request.categories && request.categories.length > 0) {
            filteredIssues = this.filterByCategories(filteredIssues, request.categories);
        }

        // Limit the number of issues
        filteredIssues = this.limitIssues(filteredIssues);

        // Sort by severity for consistent ordering
        filteredIssues = this.sortBySeverity(filteredIssues);

        return {
            ...result,
            issues: filteredIssues
        };
    }

    /**
     * Get issue statistics
     */
    getIssueStatistics(issues: CodeIssue[]): {
        total: number;
        bySeverity: Record<IssueSeverity, number>;
        byCategory: Record<IssueCategory, number>;
        criticalCount: number;
        errorCount: number;
        warningCount: number;
        infoCount: number;
    } {
        const bySeverity: Record<IssueSeverity, number> = {
            [IssueSeverity.CRITICAL]: 0,
            [IssueSeverity.ERROR]: 0,
            [IssueSeverity.WARNING]: 0,
            [IssueSeverity.INFO]: 0
        };

        const byCategory: Record<IssueCategory, number> = {
            [IssueCategory.SECURITY]: 0,
            [IssueCategory.QUALITY]: 0,
            [IssueCategory.PERFORMANCE]: 0,
            [IssueCategory.MAINTAINABILITY]: 0,
            [IssueCategory.BEST_PRACTICE]: 0,
            [IssueCategory.COMPATIBILITY]: 0,
            [IssueCategory.ACCESSIBILITY]: 0
        };

        for (const issue of issues) {
            bySeverity[issue.severity as IssueSeverity]++;
            byCategory[issue.category as IssueCategory]++;
        }

        return {
            total: issues.length,
            bySeverity,
            byCategory,
            criticalCount: bySeverity[IssueSeverity.CRITICAL],
            errorCount: bySeverity[IssueSeverity.ERROR],
            warningCount: bySeverity[IssueSeverity.WARNING],
            infoCount: bySeverity[IssueSeverity.INFO]
        };
    }
}
