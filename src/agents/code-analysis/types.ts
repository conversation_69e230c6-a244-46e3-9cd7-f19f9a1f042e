/**
 * Types and Interfaces for Code Analysis Agent
 *
 * This module contains all the type definitions, enums, and interfaces
 * used by the Code Analysis Agent and its components.
 *
 * Based on May 2025 best practices for TypeScript type definitions.
 */

import { AgentOptions } from '../base-agent.js';

/**
 * Issue Severity
 */
export enum IssueSeverity {
    INFO = 'info',
    WARNING = 'warning',
    ERROR = 'error',
    CRITICAL = 'critical'
}

/**
 * Issue Category
 */
export enum IssueCategory {
    QUALITY = 'quality',
    SECURITY = 'security',
    PERFORMANCE = 'performance',
    MAINTAINABILITY = 'maintainability',
    COMPATIBILITY = 'compatibility',
    ACCESSIBILITY = 'accessibility',
    BEST_PRACTICE = 'best_practice'
}

/**
 * Code Issue
 */
export interface CodeIssue {
    id: string;
    title: string;
    description: string;
    severity: IssueSeverity;
    category: IssueCategory;
    lineNumber?: number;
    columnNumber?: number;
    codeSnippet?: string;
    suggestion?: string;
    fixSuggestion?: string;
}

/**
 * Code Metric
 */
export interface CodeMetric {
    name: string;
    value: number;
    description: string;
    threshold?: number;
    status: 'good' | 'warning' | 'bad';
}

/**
 * Code Analysis Result
 */
export interface CodeAnalysisResult {
    issues: CodeIssue[];
    metrics: CodeMetric[];
    summary: string;
    recommendations: string[];
    codeQualityScore: number; // 0-100
}

/**
 * Code Analysis Request
 */
export interface CodeAnalysisRequest {
    code: string;
    language: string;
    filePath?: string;
    categories?: IssueCategory[];
    minSeverity?: IssueSeverity;
    includeMetrics?: boolean;
    includeRecommendations?: boolean;
    projectConventions?: string;
}

/**
 * Code Analysis Agent Options
 */
export interface CodeAnalysisAgentOptions extends AgentOptions {
    defaultCategories?: IssueCategory[];
    defaultMinSeverity?: IssueSeverity;
    includeMetricsByDefault?: boolean;
    includeRecommendationsByDefault?: boolean;
    maxIssues?: number;
}

/**
 * Code Block extracted from text
 */
export interface CodeBlock {
    language: string;
    code: string;
}

/**
 * Severity Order for filtering
 */
export const SEVERITY_ORDER = {
    [IssueSeverity.INFO]: 0,
    [IssueSeverity.WARNING]: 1,
    [IssueSeverity.ERROR]: 2,
    [IssueSeverity.CRITICAL]: 3
} as const;

/**
 * Language detection patterns
 */
export interface LanguagePattern {
    keywords: string[];
    extensions: string[];
    patterns: RegExp[];
}

/**
 * Language detection map
 */
export const LANGUAGE_PATTERNS: Record<string, LanguagePattern> = {
    typescript: {
        keywords: ['typescript', 'ts'],
        extensions: ['.ts', '.tsx'],
        patterns: [/interface\s+\w+/, /type\s+\w+\s*=/, /export\s+type/]
    },
    javascript: {
        keywords: ['javascript', 'js'],
        extensions: ['.js', '.jsx'],
        patterns: [/function\s+\w+/, /const\s+\w+\s*=/, /var\s+\w+/]
    },
    python: {
        keywords: ['python', 'py'],
        extensions: ['.py', '.pyw'],
        patterns: [/def\s+\w+/, /class\s+\w+/, /import\s+\w+/]
    },
    java: {
        keywords: ['java'],
        extensions: ['.java'],
        patterns: [/public\s+class/, /private\s+\w+/, /public\s+static/]
    },
    csharp: {
        keywords: ['c#', 'csharp', 'c sharp'],
        extensions: ['.cs'],
        patterns: [/public\s+class/, /namespace\s+\w+/, /using\s+\w+/]
    },
    cpp: {
        keywords: ['c++', 'cpp'],
        extensions: ['.cpp', '.cc', '.cxx'],
        patterns: [/#include\s*</, /std::/, /namespace\s+\w+/]
    },
    go: {
        keywords: ['go', 'golang'],
        extensions: ['.go'],
        patterns: [/package\s+\w+/, /func\s+\w+/, /import\s+"/]
    },
    rust: {
        keywords: ['rust'],
        extensions: ['.rs'],
        patterns: [/fn\s+\w+/, /struct\s+\w+/, /impl\s+\w+/]
    },
    ruby: {
        keywords: ['ruby'],
        extensions: ['.rb'],
        patterns: [/def\s+\w+/, /class\s+\w+/, /module\s+\w+/]
    },
    php: {
        keywords: ['php'],
        extensions: ['.php'],
        patterns: [/<\?php/, /function\s+\w+/, /class\s+\w+/]
    }
};
