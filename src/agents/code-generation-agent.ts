/**
 * Code Generation Agent for X10sion
 *
 * This module implements a specialized agent for generating code based on user requirements.
 * It analyzes requirements, generates code, and ensures the code follows best practices
 * and project conventions.
 *
 * Based on May 2025 best practices for code generation.
 */

import * as vscode from 'vscode';
import { BaseAgent, AgentOptions } from './base-agent.js';
import { WorkerPool, TaskPriority, Task } from '../parallel/worker-pool.js';
import { LLMMonitor } from '../monitoring/llm-monitor.js';
import { LanguageModelProvider } from '../llm/providers/base-provider.js';
import { PromptEnhancementAgent } from './prompt-enhancement-agent.js';
import { inferLanguage, isCompatibleLanguage } from './code-generation/language-detector.js';
import { extractCodeBlocks, extractImports, extractDependencies, extractExplanation } from './code-generation/code-parser.js';
import { createBasePrompt, createSimplePrompt, createTestPrompt, createDocumentationPrompt } from './code-generation/prompt-builder.js';

/**
 * Create a task for the worker pool
 */
function createTask<T>(fn: () => T | Promise<T>, priority: TaskPriority = TaskPriority.NORMAL): Task<T> {
    return {
        execute: async () => {
            const result = fn();
            return result instanceof Promise ? await result : result;
        },
        priority
    };
}

/**
 * Code Generation Result
 */
export interface CodeGenerationResult {
    code: string;
    language: string;
    explanation: string;
    imports: string[];
    dependencies: string[];
    tests?: string;
    documentation?: string;
}

/**
 * Code Generation Request
 */
export interface CodeGenerationRequest {
    requirements: string;
    language?: string;
    framework?: string;
    includeTests?: boolean;
    includeDocumentation?: boolean;
    contextFiles?: string[];
    projectConventions?: string;
}

/**
 * Code Generation Agent Options
 */
export interface CodeGenerationAgentOptions extends AgentOptions {
    promptEnhancementAgent?: PromptEnhancementAgent;
    defaultLanguage?: string;
    defaultFramework?: string;
    includeTestsByDefault?: boolean;
    includeDocumentationByDefault?: boolean;
    followProjectConventions?: boolean;
    maxCodeLength?: number;
}

/**
 * Code Generation Agent Implementation
 */
export class CodeGenerationAgent extends BaseAgent {
    private agentOptions: CodeGenerationAgentOptions;
    private promptEnhancementAgent?: PromptEnhancementAgent;

    constructor(
        id: string,
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor,
        llmProvider: LanguageModelProvider,
        options: CodeGenerationAgentOptions = {}
    ) {
        super(
            id,
            'Code Generation Agent',
            'Generates code based on user requirements',
            workerPool,
            llmMonitor,
            llmProvider,
            options
        );

        // Set default options
        this.agentOptions = {
            defaultLanguage: 'typescript',
            defaultFramework: 'node',
            includeTestsByDefault: true,
            includeDocumentationByDefault: true,
            followProjectConventions: true,
            maxCodeLength: 2000,
            ...options
        };

        // Store prompt enhancement agent
        this.promptEnhancementAgent = options.promptEnhancementAgent;
    }

    /**
     * Execute a task with the agent
     */
    async execute(task: string, context: any = {}): Promise<CodeGenerationResult> {
        try {
            // Parse the task as a code generation request
            const request = this.parseRequest(task, context);

            // Gather context for code generation
            const codeContext = await this.gatherContext(request);

            // Generate enhanced prompt for code generation
            const enhancedPrompt = await this.createEnhancedPrompt(request, codeContext);

            // Generate code using the LLM
            const generatedCode = await this.generateCode(enhancedPrompt);

            // Post-process the generated code
            const processedResult = await this.postProcessCode(generatedCode, request);

            // Generate tests if requested
            if (request.includeTests) {
                processedResult.tests = await this.generateTests(processedResult, request);
            }

            // Generate documentation if requested
            if (request.includeDocumentation) {
                processedResult.documentation = await this.generateDocumentation(processedResult, request);
            }

            return processedResult;
        } catch (error) {
            console.error('Error in CodeGenerationAgent.execute:', error);
            throw error;
        }
    }

    /**
     * Parse the task into a code generation request
     */
    private parseRequest(task: string, context: any): CodeGenerationRequest {
        // Start with defaults
        const request: CodeGenerationRequest = {
            requirements: task,
            language: this.agentOptions.defaultLanguage,
            framework: this.agentOptions.defaultFramework,
            includeTests: this.agentOptions.includeTestsByDefault,
            includeDocumentation: this.agentOptions.includeDocumentationByDefault,
            contextFiles: [],
            projectConventions: ''
        };

        // Override with context if provided
        if (context.language) {
            request.language = context.language;
        }

        if (context.framework) {
            request.framework = context.framework;
        }

        if (context.includeTests !== undefined) {
            request.includeTests = context.includeTests;
        }

        if (context.includeDocumentation !== undefined) {
            request.includeDocumentation = context.includeDocumentation;
        }

        if (context.contextFiles) {
            request.contextFiles = context.contextFiles;
        }

        if (context.projectConventions) {
            request.projectConventions = context.projectConventions;
        }

        // Try to infer language from the task if not specified
        if (!request.language) {
            request.language = inferLanguage(task);
        }

        return request;
    }



    /**
     * Gather context for code generation
     */
    private async gatherContext(request: CodeGenerationRequest): Promise<any[]> {
        // Use worker pool for parallel processing
        return await this.workerPool.execute(createTask(async () => {
            const contextItems: any[] = [];

            // Add project conventions if available
            if (request.projectConventions && this.agentOptions.followProjectConventions) {
                contextItems.push({
                    id: 'project-conventions',
                    content: request.projectConventions,
                    type: 'conventions',
                    metadata: { timestamp: Date.now() }
                });
            }

            // Add context files if available
            if (request.contextFiles && request.contextFiles.length > 0) {
                for (const filePath of request.contextFiles) {
                    try {
                        // Read file content
                        const document = await vscode.workspace.openTextDocument(filePath);
                        const content = document.getText();

                        contextItems.push({
                            id: `file-${filePath}`,
                            content,
                            type: 'file',
                            metadata: {
                                timestamp: Date.now(),
                                path: filePath,
                                language: document.languageId
                            }
                        });
                    } catch (error) {
                        console.warn(`Failed to read context file: ${filePath}`, error);
                    }
                }
            }

            // Add language and framework specific context
            contextItems.push({
                id: 'language-context',
                content: `The code should be written in ${request.language}${request.framework ? ` using the ${request.framework} framework` : ''}.`,
                type: 'language',
                metadata: { timestamp: Date.now() }
            });

            return contextItems;
        }, TaskPriority.HIGH));
    }

    /**
     * Create an enhanced prompt for code generation
     */
    private async createEnhancedPrompt(
        request: CodeGenerationRequest,
        contextItems: any[]
    ): Promise<string> {
        // If we have a prompt enhancement agent, use it
        if (this.promptEnhancementAgent) {
            const basePrompt = createBasePrompt(request);

            const enhancedPromptResult = await this.promptEnhancementAgent.execute(
                basePrompt,
                {
                    contextItems,
                    templateId: 'code'
                }
            );

            return enhancedPromptResult.enhancedPrompt;
        }

        // Otherwise, create a simple prompt
        return createSimplePrompt(request, contextItems);
    }



    /**
     * Generate code using the LLM
     */
    private async generateCode(prompt: string): Promise<string> {
        const completion = await this.getLLMProvider().generateCompletion(prompt, {
            maxTokens: 2000,
            temperature: 0.2, // Lower temperature for more deterministic code generation
            stopSequences: ['```', '---']
        });

        return completion.text;
    }

    /**
     * Post-process the generated code
     */
    private async postProcessCode(
        generatedCode: string,
        request: CodeGenerationRequest
    ): Promise<CodeGenerationResult> {
        // Use worker pool for parallel processing
        return await this.workerPool.execute(createTask(() => {
            // Extract code blocks
            const codeBlocks = extractCodeBlocks(generatedCode);

            // Find the main code block
            const mainCodeBlock = codeBlocks.find(block =>
                block.language === request.language ||
                isCompatibleLanguage(block.language, request.language!)
            ) || codeBlocks[0];

            // Extract imports and dependencies
            const imports = extractImports(mainCodeBlock.code, request.language!);
            const dependencies = extractDependencies(mainCodeBlock.code, request.language!);

            // Extract explanation
            const explanation = extractExplanation(generatedCode);

            return {
                code: mainCodeBlock.code,
                language: mainCodeBlock.language || request.language!,
                explanation,
                imports,
                dependencies
            };
        }, TaskPriority.NORMAL));
    }




    /**
     * Generate tests for the code
     */
    private async generateTests(
        result: CodeGenerationResult,
        request: CodeGenerationRequest
    ): Promise<string> {
        // Create a prompt for test generation using the imported function
        const testPrompt = createTestPrompt(result.code, request.language!, request.framework);

        try {
            // Generate tests using the LLM
            const completion = await this.getLLMProvider().generateCompletion(testPrompt, {
                maxTokens: 1000,
                temperature: 0.2
            });

            // Check if completion is valid
            if (!completion || !completion.text) {
                console.warn('LLM returned empty or invalid completion for test generation');
                return `// TODO: Add tests for this code\n// Test generation failed - please implement tests manually`;
            }

            // Extract code blocks from the response
            const codeBlocks = extractCodeBlocks(completion.text);

            // Check if we have any code blocks
            if (!codeBlocks || codeBlocks.length === 0) {
                console.warn('No code blocks found in test generation response');
                return completion.text; // Return the raw text if no code blocks found
            }

            // Find the test code block
            const testCodeBlock = codeBlocks.find(block =>
                block.language === request.language ||
                isCompatibleLanguage(block.language, request.language!)
            ) || codeBlocks[0];

            return testCodeBlock.code;
        } catch (error) {
            console.error('Error generating tests:', error);
            return `// TODO: Add tests for this code\n// Test generation failed - please implement tests manually`;
        }
    }

    /**
     * Generate documentation for the code
     */
    private async generateDocumentation(
        result: CodeGenerationResult,
        request: CodeGenerationRequest
    ): Promise<string> {
        // Create a prompt for documentation generation using the imported function
        const docPrompt = createDocumentationPrompt(result.code, request.language!, true);

        try {
            // Generate documentation using the LLM
            const completion = await this.getLLMProvider().generateCompletion(docPrompt, {
                maxTokens: 1000,
                temperature: 0.2
            });

            // Check if completion is valid
            if (!completion || !completion.text) {
                console.warn('LLM returned empty or invalid completion for documentation generation');
                return `// TODO: Add documentation for this code\n// Documentation generation failed - please document manually`;
            }

            return completion.text.trim();
        } catch (error) {
            console.error('Error generating documentation:', error);
            return `// TODO: Add documentation for this code\n// Documentation generation failed - please document manually`;
        }
    }
}
