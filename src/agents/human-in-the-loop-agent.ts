/**
 * Human-in-the-Loop Agent for X10sion
 *
 * This module implements a human-in-the-loop system for AI agents, allowing for
 * human intervention, approval, and feedback at various stages of AI operation.
 *
 * Based on May 2025 best practices for human-in-the-loop AI systems.
 * Refactored into modular components for better maintainability.
 */

import * as vscode from 'vscode';
import { BaseAgent } from './base-agent.js';
import { LLMMonitor } from '../monitoring/llm-monitor.js';
import { WorkerPool } from '../parallel/worker-pool.js';
import { LanguageModelProvider } from '../llm/providers/base-provider.js';

// Import modular components
import {
    InterventionLevel,
    ActionType,
    InterventionRequest,
    InterventionResponse,
    HumanInTheLoopCapabilities,
    InterventionConfig
} from './human-in-the-loop/types.js';
import { InterventionManager } from './human-in-the-loop/intervention-manager.js';
import { UIHandler } from './human-in-the-loop/ui-handler.js';
import { AdaptiveLearning } from './human-in-the-loop/adaptive-learning.js';

// Re-export types for external use
export {
    InterventionLevel,
    ActionType,
    InterventionRequest,
    InterventionResponse,
    HumanInTheLoopCapabilities
} from './human-in-the-loop/types.js';

/**
 * Human-in-the-Loop Agent implementation
 */
export class HumanInTheLoopAgent extends BaseAgent {
    private capabilities: HumanInTheLoopCapabilities;
    private interventionManager: InterventionManager;
    private uiHandler: UIHandler;
    private adaptiveLearning: AdaptiveLearning;
    // Use protected instead of private to match the base class
    protected localDisposables: vscode.Disposable[] = [];

    // Singleton instance for global access
    private static instance: HumanInTheLoopAgent | null = null;

    constructor(
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor,
        llmProvider: LanguageModelProvider,
        capabilities: HumanInTheLoopCapabilities
    ) {
        super(
            'human-in-the-loop',
            'Human-in-the-Loop Agent',
            'Manages human intervention in AI agent operations',
            workerPool,
            llmMonitor,
            llmProvider,
            {}
        );

        this.capabilities = capabilities;

        // Initialize modular components
        this.interventionManager = new InterventionManager(capabilities);
        this.uiHandler = new UIHandler(capabilities);
        this.adaptiveLearning = new AdaptiveLearning(capabilities);

        this.initialize();

        // Set singleton instance
        HumanInTheLoopAgent.instance = this;
    }

    /**
     * Get singleton instance
     */
    static getInstance(): HumanInTheLoopAgent | null {
        return HumanInTheLoopAgent.instance;
    }

    private initialize(): void {
        // Register commands for human intervention
        this.localDisposables.push(
            vscode.commands.registerCommand('x10sion.approveAction', async (requestId: string) => {
                await this.handleApproval(requestId, true);
            }),
            vscode.commands.registerCommand('x10sion.rejectAction', async (requestId: string) => {
                await this.handleApproval(requestId, false);
            }),
            vscode.commands.registerCommand('x10sion.provideGuidance', async (requestId: string) => {
                await this.handleGuidance(requestId);
            })
        );

        // Add to base class disposables
        this.disposables.push(...this.localDisposables);
    }

    /**
     * Request human intervention for an action
     */
    public async requestIntervention(
        actionType: ActionType,
        description: string,
        suggestedAction?: string,
        alternatives?: string[],
        context?: any,
        level?: InterventionLevel
    ): Promise<InterventionResponse> {
        const startTime = Date.now();

        // Create intervention request using the manager
        const request = this.interventionManager.createInterventionRequest({
            actionType,
            description,
            suggestedAction,
            alternatives,
            context,
            level
        });

        // If no intervention needed or auto-approvable, return immediately
        if (request.level === InterventionLevel.NONE || this.interventionManager.isAutoApprovable(request)) {
            const response = this.interventionManager.createAutoApprovedResponse(
                request.id,
                request.level === InterventionLevel.NONE ? 'No intervention required' : 'Auto-approved'
            );
            return response;
        }

        let response: InterventionResponse;

        // Handle the intervention based on level using UI handler
        try {
            switch (request.level) {
                case InterventionLevel.NOTIFICATION:
                    response = await this.uiHandler.showNotification(request);
                    break;

                case InterventionLevel.APPROVAL:
                    response = await this.uiHandler.requestApproval(request);
                    break;

                case InterventionLevel.GUIDANCE:
                    response = await this.uiHandler.requestGuidance(request);
                    break;

                case InterventionLevel.TAKEOVER:
                    response = await this.uiHandler.requestTakeover(request);
                    break;

                default:
                    response = {
                        requestId: request.id,
                        timestamp: Date.now(),
                        approved: false,
                        feedback: 'Unknown intervention level'
                    };
            }
        } catch (error) {
            // Handle timeout or other errors
            response = this.interventionManager.handleTimeout(request.id);
        }

        // Store the response and record for learning
        this.interventionManager.storeResponse(response);

        const responseTime = Date.now() - startTime;
        this.adaptiveLearning.recordResponse(response, actionType, responseTime);

        // Adjust intervention levels if adaptive mode is enabled
        if (this.capabilities.adaptiveMode) {
            this.adaptiveLearning.adjustInterventionLevels(actionType, response.approved);
        }

        return response;
    }

    /**
     * Get the intervention level for a specific action type
     */
    getInterventionLevelForAction(actionType: ActionType): InterventionLevel {
        return this.interventionManager.getInterventionLevelForAction(actionType);
    }

    /**
     * Get pending interventions
     */
    getPendingInterventions(): InterventionRequest[] {
        return this.interventionManager.getPendingInterventions();
    }

    /**
     * Get intervention statistics
     */
    getInterventionStatistics() {
        return this.interventionManager.getStatistics();
    }

    /**
     * Get learning statistics
     */
    getLearningStatistics() {
        return this.adaptiveLearning.getOverallStatistics();
    }

    /**
     * Update capabilities for all components
     */
    updateCapabilities(capabilities: Partial<HumanInTheLoopCapabilities>): void {
        this.capabilities = { ...this.capabilities, ...capabilities };
        this.interventionManager.updateCapabilities(this.capabilities);
        this.uiHandler.updateCapabilities(this.capabilities);
        this.adaptiveLearning.updateCapabilities(this.capabilities);
    }

    /**
     * Handle approval response from UI commands
     */
    private async handleApproval(requestId: string, approved: boolean): Promise<void> {
        const request = this.interventionManager.getPendingIntervention(requestId);
        if (!request) {
            return;
        }

        let feedback = '';
        if (this.capabilities.collectFeedback) {
            feedback = await this.uiHandler.collectFeedback(
                requestId,
                `Optional feedback for ${approved ? 'approval' : 'rejection'}`,
                'Enter feedback or leave empty'
            );
        }

        const response: InterventionResponse = {
            requestId,
            timestamp: Date.now(),
            approved,
            feedback
        };

        this.interventionManager.storeResponse(response);

        // Record for learning and adjust if adaptive mode is enabled
        if (this.capabilities.adaptiveMode) {
            this.adaptiveLearning.recordResponse(response, request.actionType, 0);
            this.adaptiveLearning.adjustInterventionLevels(request.actionType, approved);
        }
    }

    /**
     * Handle guidance response from UI commands
     */
    private async handleGuidance(requestId: string): Promise<void> {
        const request = this.interventionManager.getPendingIntervention(requestId);
        if (!request) {
            return;
        }

        const guidance = await this.uiHandler.collectFeedback(
            requestId,
            request.description,
            request.suggestedAction || 'Enter your guidance here'
        );

        if (!guidance) {
            // User cancelled
            return;
        }

        const response: InterventionResponse = {
            requestId,
            timestamp: Date.now(),
            approved: true,
            modifiedAction: guidance
        };

        this.interventionManager.storeResponse(response);

        // Record for learning
        if (this.capabilities.adaptiveMode) {
            this.adaptiveLearning.recordResponse(response, request.actionType, 0);
        }
    }

    /**
     * Get approval rate for an action type (delegated to adaptive learning)
     */
    getApprovalRate(actionType: ActionType): number {
        return this.adaptiveLearning.getApprovalRate(actionType);
    }

    /**
     * Predict intervention level for an action type
     */
    predictInterventionLevel(actionType: ActionType) {
        return this.adaptiveLearning.predictInterventionLevel(actionType);
    }

    /**
     * Reset learning data
     */
    resetLearningData(actionType?: ActionType): void {
        if (actionType) {
            this.adaptiveLearning.resetLearningData(actionType);
        } else {
            this.adaptiveLearning.resetAllLearningData();
        }
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
    }
}
