/**
 * Adaptive Learning Module for Human-in-the-Loop Agent
 *
 * This module implements adaptive learning algorithms that adjust
 * intervention levels based on user feedback and behavior patterns.
 *
 * Based on May 2025 best practices for adaptive AI systems.
 */

import {
    InterventionLevel,
    ActionType,
    InterventionResponse,
    HumanInTheLoopCapabilities,
    AdaptiveLearningData,
    APPROVAL_THRESHOLDS,
    INTERVENTION_HIERARCHY
} from './types.js';

/**
 * Adaptive Learning class for managing intervention level adjustments
 */
export class AdaptiveLearning {
    private capabilities: HumanInTheLoopCapabilities;
    private learningData: Map<ActionType, AdaptiveLearningData> = new Map();
    private responseHistory: InterventionResponse[] = [];

    constructor(capabilities: HumanInTheLoopCapabilities) {
        this.capabilities = capabilities;
        this.initializeLearningData();
    }

    /**
     * Initialize learning data for all action types
     */
    private initializeLearningData(): void {
        for (const actionType of Object.values(ActionType)) {
            this.learningData.set(actionType, {
                actionType,
                approvalHistory: [],
                averageResponseTime: 0,
                lastAdjustment: Date.now(),
                confidenceScore: 0.5 // Start with neutral confidence
            });
        }
    }

    /**
     * Record a new intervention response for learning
     */
    recordResponse(response: InterventionResponse, actionType: ActionType, responseTime: number): void {
        // Add to response history
        this.responseHistory.push(response);
        
        // Keep only the last 100 responses to prevent memory issues
        if (this.responseHistory.length > 100) {
            this.responseHistory = this.responseHistory.slice(-100);
        }

        // Update learning data for this action type
        const data = this.learningData.get(actionType);
        if (data) {
            data.approvalHistory.push(response.approved);
            
            // Keep only the last 20 approvals for this action type
            if (data.approvalHistory.length > 20) {
                data.approvalHistory = data.approvalHistory.slice(-20);
            }

            // Update average response time
            data.averageResponseTime = this.calculateAverageResponseTime(data.averageResponseTime, responseTime);
            
            // Update confidence score based on consistency
            data.confidenceScore = this.calculateConfidenceScore(data.approvalHistory);
        }
    }

    /**
     * Adjust intervention levels based on user feedback (adaptive mode)
     */
    adjustInterventionLevels(actionType: ActionType, approved: boolean): boolean {
        if (!this.capabilities.adaptiveMode) {
            return false;
        }

        const data = this.learningData.get(actionType);
        if (!data || data.approvalHistory.length < APPROVAL_THRESHOLDS.MIN_SAMPLES) {
            return false; // Not enough data to make adjustments
        }

        const currentLevel = this.capabilities.actionTypeSettings.get(actionType) ||
                            this.capabilities.defaultInterventionLevel;
        
        const approvalRate = this.getApprovalRate(actionType);
        const timeSinceLastAdjustment = Date.now() - data.lastAdjustment;
        
        // Only adjust if enough time has passed (prevent oscillation)
        if (timeSinceLastAdjustment < 60000) { // 1 minute minimum
            return false;
        }

        let newLevel: InterventionLevel | null = null;

        // If user consistently approves, gradually reduce intervention level
        if (approvalRate >= APPROVAL_THRESHOLDS.REDUCE_INTERVENTION && data.confidenceScore > 0.7) {
            newLevel = this.reduceInterventionLevel(currentLevel);
        }
        // If user frequently rejects, increase intervention level
        else if (approvalRate <= APPROVAL_THRESHOLDS.INCREASE_INTERVENTION && data.confidenceScore > 0.7) {
            newLevel = this.increaseInterventionLevel(currentLevel);
        }

        if (newLevel && newLevel !== currentLevel) {
            this.capabilities.actionTypeSettings.set(actionType, newLevel);
            data.lastAdjustment = Date.now();
            
            console.log(`Adjusted intervention level for ${actionType}: ${currentLevel} -> ${newLevel} (approval rate: ${approvalRate.toFixed(2)})`);
            return true;
        }

        return false;
    }

    /**
     * Calculate approval rate for a specific action type
     */
    getApprovalRate(actionType: ActionType): number {
        const data = this.learningData.get(actionType);
        if (!data || data.approvalHistory.length === 0) {
            return 0;
        }

        const approved = data.approvalHistory.filter(a => a).length;
        return approved / data.approvalHistory.length;
    }

    /**
     * Get learning statistics for an action type
     */
    getLearningStatistics(actionType: ActionType): AdaptiveLearningData | null {
        return this.learningData.get(actionType) || null;
    }

    /**
     * Get overall learning statistics
     */
    getOverallStatistics(): {
        totalResponses: number;
        overallApprovalRate: number;
        averageConfidence: number;
        actionTypeStats: Map<ActionType, AdaptiveLearningData>;
    } {
        const totalResponses = this.responseHistory.length;
        const overallApprovalRate = totalResponses > 0 ?
            this.responseHistory.filter(r => r.approved).length / totalResponses : 0;

        const confidenceScores = Array.from(this.learningData.values()).map(d => d.confidenceScore);
        const averageConfidence = confidenceScores.length > 0 ?
            confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length : 0;

        return {
            totalResponses,
            overallApprovalRate,
            averageConfidence,
            actionTypeStats: new Map(this.learningData)
        };
    }

    /**
     * Reset learning data for an action type
     */
    resetLearningData(actionType: ActionType): void {
        this.learningData.set(actionType, {
            actionType,
            approvalHistory: [],
            averageResponseTime: 0,
            lastAdjustment: Date.now(),
            confidenceScore: 0.5
        });
    }

    /**
     * Reset all learning data
     */
    resetAllLearningData(): void {
        this.learningData.clear();
        this.responseHistory = [];
        this.initializeLearningData();
    }

    /**
     * Reduce intervention level by one step
     */
    private reduceInterventionLevel(currentLevel: InterventionLevel): InterventionLevel | null {
        const currentIndex = INTERVENTION_HIERARCHY.indexOf(currentLevel);
        if (currentIndex > 0) {
            return INTERVENTION_HIERARCHY[currentIndex - 1];
        }
        return null; // Already at the lowest level
    }

    /**
     * Increase intervention level by one step
     */
    private increaseInterventionLevel(currentLevel: InterventionLevel): InterventionLevel | null {
        const currentIndex = INTERVENTION_HIERARCHY.indexOf(currentLevel);
        if (currentIndex < INTERVENTION_HIERARCHY.length - 1) {
            return INTERVENTION_HIERARCHY[currentIndex + 1];
        }
        return null; // Already at the highest level
    }

    /**
     * Calculate average response time with exponential smoothing
     */
    private calculateAverageResponseTime(currentAverage: number, newTime: number): number {
        const alpha = 0.2; // Smoothing factor
        return currentAverage === 0 ? newTime : (alpha * newTime) + ((1 - alpha) * currentAverage);
    }

    /**
     * Calculate confidence score based on approval history consistency
     */
    private calculateConfidenceScore(approvalHistory: boolean[]): number {
        if (approvalHistory.length < 3) {
            return 0.5; // Neutral confidence with insufficient data
        }

        // Calculate variance in approval decisions
        const approvalRate = approvalHistory.filter(a => a).length / approvalHistory.length;
        
        // High confidence if approval rate is consistently high or low
        if (approvalRate >= 0.8 || approvalRate <= 0.2) {
            return 0.9;
        } else if (approvalRate >= 0.7 || approvalRate <= 0.3) {
            return 0.7;
        } else {
            return 0.5; // Neutral confidence for mixed results
        }
    }

    /**
     * Predict intervention level for an action type
     */
    predictInterventionLevel(actionType: ActionType): {
        predictedLevel: InterventionLevel;
        confidence: number;
        reasoning: string;
    } {
        const data = this.learningData.get(actionType);
        const currentLevel = this.capabilities.actionTypeSettings.get(actionType) ||
                            this.capabilities.defaultInterventionLevel;

        if (!data || data.approvalHistory.length < APPROVAL_THRESHOLDS.MIN_SAMPLES) {
            return {
                predictedLevel: currentLevel,
                confidence: 0.5,
                reasoning: 'Insufficient data for prediction'
            };
        }

        const approvalRate = this.getApprovalRate(actionType);
        let predictedLevel = currentLevel;
        let reasoning = 'Current level maintained';

        if (approvalRate >= APPROVAL_THRESHOLDS.REDUCE_INTERVENTION && data.confidenceScore > 0.7) {
            const reduced = this.reduceInterventionLevel(currentLevel);
            if (reduced) {
                predictedLevel = reduced;
                reasoning = `High approval rate (${(approvalRate * 100).toFixed(1)}%) suggests level reduction`;
            }
        } else if (approvalRate <= APPROVAL_THRESHOLDS.INCREASE_INTERVENTION && data.confidenceScore > 0.7) {
            const increased = this.increaseInterventionLevel(currentLevel);
            if (increased) {
                predictedLevel = increased;
                reasoning = `Low approval rate (${(approvalRate * 100).toFixed(1)}%) suggests level increase`;
            }
        }

        return {
            predictedLevel,
            confidence: data.confidenceScore,
            reasoning
        };
    }

    /**
     * Update capabilities
     */
    updateCapabilities(capabilities: HumanInTheLoopCapabilities): void {
        this.capabilities = capabilities;
    }
}
