/**
 * Intervention Manager for Human-in-the-Loop Agent
 *
 * This module handles the core logic for managing intervention requests,
 * tracking responses, and coordinating with UI handlers.
 *
 * Based on May 2025 best practices for intervention management.
 */

import {
    InterventionRequest,
    InterventionResponse,
    InterventionLevel,
    ActionType,
    HumanInTheLoopCapabilities,
    InterventionConfig,
    InterventionStatistics,
    InterventionEvent,
    InterventionEventType,
    LOW_RISK_ACTIONS,
    DEFAULT_TIMEOUTS
} from './types.js';

/**
 * Intervention Manager class for handling intervention lifecycle
 */
export class InterventionManager {
    private pendingInterventions: Map<string, InterventionRequest> = new Map();
    private interventionResponses: Map<string, InterventionResponse> = new Map();
    private interventionEvents: InterventionEvent[] = [];
    private capabilities: HumanInTheLoopCapabilities;

    constructor(capabilities: HumanInTheLoopCapabilities) {
        this.capabilities = capabilities;
    }

    /**
     * Create a new intervention request
     */
    createInterventionRequest(config: InterventionConfig): InterventionRequest {
        const interventionLevel = config.level || this.getInterventionLevelForAction(config.actionType);
        
        const request: InterventionRequest = {
            id: `req-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            timestamp: Date.now(),
            actionType: config.actionType,
            description: config.description,
            suggestedAction: config.suggestedAction,
            alternatives: config.alternatives,
            context: config.context,
            level: interventionLevel,
            timeout: config.timeout || this.getTimeoutForLevel(interventionLevel)
        };

        // Store the request
        this.pendingInterventions.set(request.id, request);

        // Log the event
        this.logEvent({
            type: InterventionEventType.REQUEST_CREATED,
            requestId: request.id,
            timestamp: Date.now(),
            actionType: request.actionType,
            level: request.level
        });

        return request;
    }

    /**
     * Get the intervention level for a specific action type
     */
    getInterventionLevelForAction(actionType: ActionType): InterventionLevel {
        return this.capabilities.actionTypeSettings.get(actionType) ||
               this.capabilities.defaultInterventionLevel;
    }

    /**
     * Get timeout for intervention level
     */
    private getTimeoutForLevel(level: InterventionLevel): number {
        return DEFAULT_TIMEOUTS[level] || this.capabilities.timeoutMs;
    }

    /**
     * Check if an action requires intervention
     */
    requiresIntervention(actionType: ActionType): boolean {
        const level = this.getInterventionLevelForAction(actionType);
        return level !== InterventionLevel.NONE;
    }

    /**
     * Check if an action is auto-approvable
     */
    isAutoApprovable(request: InterventionRequest): boolean {
        if (request.level === InterventionLevel.NONE) {
            return true;
        }

        if (request.level === InterventionLevel.NOTIFICATION) {
            return true;
        }

        // Check if it's a low-risk action and auto-approval is enabled
        return this.capabilities.allowAutoApprovalForLowRisk &&
               this.isLowRiskAction(request.actionType);
    }

    /**
     * Create an auto-approved response
     */
    createAutoApprovedResponse(requestId: string, reason: string = 'Auto-approved'): InterventionResponse {
        const response: InterventionResponse = {
            requestId,
            timestamp: Date.now(),
            approved: true,
            feedback: reason
        };

        this.interventionResponses.set(requestId, response);
        this.pendingInterventions.delete(requestId);

        this.logEvent({
            type: InterventionEventType.REQUEST_APPROVED,
            requestId,
            timestamp: Date.now(),
            actionType: this.pendingInterventions.get(requestId)?.actionType || ActionType.CODE_GENERATION,
            level: this.pendingInterventions.get(requestId)?.level || InterventionLevel.NONE,
            metadata: { autoApproved: true, reason }
        });

        return response;
    }

    /**
     * Store intervention response
     */
    storeResponse(response: InterventionResponse): void {
        this.interventionResponses.set(response.requestId, response);
        
        const request = this.pendingInterventions.get(response.requestId);
        if (request) {
            this.pendingInterventions.delete(response.requestId);

            // Log the event
            this.logEvent({
                type: response.approved ? InterventionEventType.REQUEST_APPROVED : InterventionEventType.REQUEST_REJECTED,
                requestId: response.requestId,
                timestamp: Date.now(),
                actionType: request.actionType,
                level: request.level,
                metadata: { 
                    feedback: response.feedback,
                    modifiedAction: response.modifiedAction,
                    selectedAlternative: response.selectedAlternative
                }
            });
        }
    }

    /**
     * Handle timeout for intervention request
     */
    handleTimeout(requestId: string): InterventionResponse {
        const request = this.pendingInterventions.get(requestId);
        if (!request) {
            throw new Error(`Request ${requestId} not found`);
        }

        // Auto-approve low-risk actions on timeout if configured
        const isLowRisk = this.isLowRiskAction(request.actionType);
        const approved = this.capabilities.allowAutoApprovalForLowRisk && isLowRisk;

        const response: InterventionResponse = {
            requestId,
            timestamp: Date.now(),
            approved,
            feedback: approved ? 'Auto-approved (timeout)' : 'Auto-rejected (timeout)'
        };

        this.storeResponse(response);

        this.logEvent({
            type: InterventionEventType.REQUEST_TIMEOUT,
            requestId,
            timestamp: Date.now(),
            actionType: request.actionType,
            level: request.level,
            metadata: { autoApproved: approved }
        });

        return response;
    }

    /**
     * Get pending intervention by ID
     */
    getPendingIntervention(requestId: string): InterventionRequest | undefined {
        return this.pendingInterventions.get(requestId);
    }

    /**
     * Get intervention response by ID
     */
    getInterventionResponse(requestId: string): InterventionResponse | undefined {
        return this.interventionResponses.get(requestId);
    }

    /**
     * Get all pending interventions
     */
    getPendingInterventions(): InterventionRequest[] {
        return Array.from(this.pendingInterventions.values());
    }

    /**
     * Check if an action type is considered low risk
     */
    isLowRiskAction(actionType: ActionType): boolean {
        return LOW_RISK_ACTIONS.includes(actionType);
    }

    /**
     * Get intervention statistics
     */
    getStatistics(): InterventionStatistics {
        const responses = Array.from(this.interventionResponses.values());
        const totalRequests = responses.length;
        const approvedRequests = responses.filter(r => r.approved).length;
        const rejectedRequests = responses.filter(r => !r.approved).length;
        
        // Count timeout requests from events
        const timeoutRequests = this.interventionEvents.filter(
            e => e.type === InterventionEventType.REQUEST_TIMEOUT
        ).length;

        // Calculate average response time
        const responseTimes = responses.map(r => {
            const request = this.pendingInterventions.get(r.requestId);
            return request ? r.timestamp - request.timestamp : 0;
        }).filter(time => time > 0);

        const averageResponseTime = responseTimes.length > 0 ?
            responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;

        // Calculate approval rate by action type
        const approvalRateByActionType = new Map<ActionType, number>();
        for (const actionType of Object.values(ActionType)) {
            const actionResponses = responses.filter(r => {
                const request = this.pendingInterventions.get(r.requestId);
                return request?.actionType === actionType;
            });

            if (actionResponses.length > 0) {
                const approvalRate = actionResponses.filter(r => r.approved).length / actionResponses.length;
                approvalRateByActionType.set(actionType, approvalRate);
            }
        }

        return {
            totalRequests,
            approvedRequests,
            rejectedRequests,
            timeoutRequests,
            averageResponseTime,
            approvalRateByActionType
        };
    }

    /**
     * Clear old intervention data
     */
    clearOldData(maxAge: number = 24 * 60 * 60 * 1000): void { // Default: 24 hours
        const cutoffTime = Date.now() - maxAge;

        // Clear old responses
        for (const [requestId, response] of this.interventionResponses) {
            if (response.timestamp < cutoffTime) {
                this.interventionResponses.delete(requestId);
            }
        }

        // Clear old events
        this.interventionEvents = this.interventionEvents.filter(
            event => event.timestamp >= cutoffTime
        );
    }

    /**
     * Log intervention event
     */
    private logEvent(event: InterventionEvent): void {
        this.interventionEvents.push(event);
        
        // Keep only the last 1000 events to prevent memory issues
        if (this.interventionEvents.length > 1000) {
            this.interventionEvents = this.interventionEvents.slice(-1000);
        }
    }

    /**
     * Get intervention events
     */
    getEvents(limit?: number): InterventionEvent[] {
        const events = [...this.interventionEvents].reverse(); // Most recent first
        return limit ? events.slice(0, limit) : events;
    }

    /**
     * Update capabilities
     */
    updateCapabilities(capabilities: Partial<HumanInTheLoopCapabilities>): void {
        this.capabilities = { ...this.capabilities, ...capabilities };
    }
}
