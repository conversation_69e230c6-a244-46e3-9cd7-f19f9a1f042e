/**
 * Types and Interfaces for Human-in-the-Loop Agent
 *
 * This module contains all the type definitions, enums, and interfaces
 * used by the Human-in-the-Loop Agent and its components.
 *
 * Based on May 2025 best practices for human-AI interaction.
 */

// Intervention levels for human-in-the-loop
export enum InterventionLevel {
    NONE = 'none',               // No human intervention required
    NOTIFICATION = 'notification', // Notify human but proceed
    APPROVAL = 'approval',       // Require human approval before proceeding
    GUIDANCE = 'guidance',       // Request human guidance/input
    TAKEOVER = 'takeover'        // Human takes over the task completely
}

// Types of actions that might require human intervention
export enum ActionType {
    CODE_GENERATION = 'code_generation',
    CODE_MODIFICATION = 'code_modification',
    FILE_SYSTEM_ACCESS = 'file_system_access',
    EXTERNAL_API_CALL = 'external_api_call',
    SENSITIVE_DATA_ACCESS = 'sensitive_data_access',
    SECURITY_CRITICAL = 'security_critical',
    RESOURCE_INTENSIVE = 'resource_intensive',
    UNCERTAIN_OUTPUT = 'uncertain_output'
}

// Interface for human intervention request
export interface InterventionRequest {
    id: string;
    timestamp: number;
    actionType: ActionType;
    description: string;
    suggestedAction?: string;
    alternatives?: string[];
    context?: any;
    level: InterventionLevel;
    timeout?: number; // Timeout in milliseconds
}

// Interface for human intervention response
export interface InterventionResponse {
    requestId: string;
    timestamp: number;
    approved: boolean;
    feedback?: string;
    modifiedAction?: string;
    selectedAlternative?: number;
    rejected?: boolean; // For compatibility with existing tests
}

// Human-in-the-Loop Agent capabilities
export interface HumanInTheLoopCapabilities {
    defaultInterventionLevel: InterventionLevel;
    actionTypeSettings: Map<ActionType, InterventionLevel>;
    timeoutMs: number;
    allowAutoApprovalForLowRisk: boolean;
    collectFeedback: boolean;
    adaptiveMode: boolean; // Adjust intervention levels based on feedback
}

// Configuration for intervention requests
export interface InterventionConfig {
    actionType: ActionType;
    description: string;
    suggestedAction?: string;
    alternatives?: string[];
    context?: any;
    level?: InterventionLevel;
    timeout?: number;
}

// Statistics for intervention tracking
export interface InterventionStatistics {
    totalRequests: number;
    approvedRequests: number;
    rejectedRequests: number;
    timeoutRequests: number;
    averageResponseTime: number;
    approvalRateByActionType: Map<ActionType, number>;
}

// Adaptive learning data
export interface AdaptiveLearningData {
    actionType: ActionType;
    approvalHistory: boolean[];
    averageResponseTime: number;
    lastAdjustment: number;
    confidenceScore: number;
}

// UI interaction options
export interface UIInteractionOptions {
    modal?: boolean;
    timeout?: number;
    showProgress?: boolean;
    allowCancel?: boolean;
    collectFeedback?: boolean;
}

// Low-risk action types for auto-approval
export const LOW_RISK_ACTIONS: ActionType[] = [
    ActionType.CODE_GENERATION, // Just generating code, not modifying
    ActionType.UNCERTAIN_OUTPUT // Uncertain but not harmful
];

// High-risk action types that always require approval
export const HIGH_RISK_ACTIONS: ActionType[] = [
    ActionType.SENSITIVE_DATA_ACCESS,
    ActionType.SECURITY_CRITICAL,
    ActionType.FILE_SYSTEM_ACCESS
];

// Default timeout values for different intervention levels
export const DEFAULT_TIMEOUTS: Record<InterventionLevel, number> = {
    [InterventionLevel.NONE]: 0,
    [InterventionLevel.NOTIFICATION]: 5000,    // 5 seconds
    [InterventionLevel.APPROVAL]: 30000,       // 30 seconds
    [InterventionLevel.GUIDANCE]: 60000,       // 1 minute
    [InterventionLevel.TAKEOVER]: 120000       // 2 minutes
};

// Intervention level hierarchy for adaptive adjustments
export const INTERVENTION_HIERARCHY: InterventionLevel[] = [
    InterventionLevel.NONE,
    InterventionLevel.NOTIFICATION,
    InterventionLevel.APPROVAL,
    InterventionLevel.GUIDANCE,
    InterventionLevel.TAKEOVER
];

// Approval rate thresholds for adaptive learning
export const APPROVAL_THRESHOLDS = {
    REDUCE_INTERVENTION: 0.8,  // 80% approval rate to reduce intervention
    INCREASE_INTERVENTION: 0.3, // 30% approval rate to increase intervention
    MIN_SAMPLES: 5             // Minimum number of samples before adjusting
};

// Event types for intervention lifecycle
export enum InterventionEventType {
    REQUEST_CREATED = 'request_created',
    REQUEST_APPROVED = 'request_approved',
    REQUEST_REJECTED = 'request_rejected',
    REQUEST_TIMEOUT = 'request_timeout',
    REQUEST_CANCELLED = 'request_cancelled',
    LEVEL_ADJUSTED = 'level_adjusted'
}

// Event data for intervention tracking
export interface InterventionEvent {
    type: InterventionEventType;
    requestId: string;
    timestamp: number;
    actionType: ActionType;
    level: InterventionLevel;
    metadata?: any;
}
