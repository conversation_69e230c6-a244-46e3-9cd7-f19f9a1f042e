/**
 * UI Handler for Human-in-the-Loop Agent
 *
 * This module handles all user interface interactions for intervention
 * requests, including notifications, approvals, and guidance collection.
 *
 * Based on May 2025 best practices for VS Code UI integration.
 */

import * as vscode from 'vscode';
import {
    InterventionRequest,
    InterventionResponse,
    InterventionLevel,
    HumanInTheLoopCapabilities,
    UIInteractionOptions
} from './types.js';

/**
 * UI Handler class for managing user interactions
 */
export class UIHandler {
    private capabilities: HumanInTheLoopCapabilities;

    constructor(capabilities: HumanInTheLoopCapabilities) {
        this.capabilities = capabilities;
    }

    /**
     * Show a notification to the user about an action
     */
    async showNotification(request: InterventionRequest): Promise<InterventionResponse> {
        await vscode.window.showInformationMessage(
            `X10sion AI is performing: ${request.description}`,
            'OK'
        );

        return {
            requestId: request.id,
            timestamp: Date.now(),
            approved: true,
            feedback: 'Notification acknowledged'
        };
    }

    /**
     * Request approval from the user for an action
     */
    async requestApproval(
        request: InterventionRequest,
        options: UIInteractionOptions = {}
    ): Promise<InterventionResponse> {
        return new Promise<InterventionResponse>((resolve) => {
            const timeout = options.timeout || request.timeout || this.capabilities.timeoutMs;
            
            // Set timeout handler
            const timeoutHandler = setTimeout(() => {
                // Auto-approve low-risk actions on timeout if configured
                const isLowRisk = this.isLowRiskAction(request);
                const approved = this.capabilities.allowAutoApprovalForLowRisk && isLowRisk;

                resolve({
                    requestId: request.id,
                    timestamp: Date.now(),
                    approved,
                    feedback: approved ? 'Auto-approved (timeout)' : 'Auto-rejected (timeout)'
                });
            }, timeout);

            // Show approval request
            const message = `X10sion AI needs approval: ${request.description}`;
            const approveButton = 'Approve';
            const rejectButton = 'Reject';
            const detailsButton = 'Details';

            const buttons = [approveButton, rejectButton];
            if (request.context || request.suggestedAction) {
                buttons.push(detailsButton);
            }

            vscode.window.showInformationMessage(
                message,
                { modal: options.modal },
                ...buttons
            ).then(async (selection) => {
                clearTimeout(timeoutHandler);

                if (selection === detailsButton) {
                    // Show detailed information and ask again
                    await this.showDetails(request);
                    const retryResponse = await this.requestApproval(request, options);
                    resolve(retryResponse);
                    return;
                }

                const approved = selection === approveButton;
                let feedback = '';

                if (this.capabilities.collectFeedback && approved && options.collectFeedback !== false) {
                    feedback = await vscode.window.showInputBox({
                        prompt: 'Optional feedback for this approval',
                        placeHolder: 'Enter feedback or leave empty'
                    }) || '';
                }

                const response: InterventionResponse = {
                    requestId: request.id,
                    timestamp: Date.now(),
                    approved,
                    feedback
                };

                resolve(response);
            });
        });
    }

    /**
     * Request guidance from the user
     */
    async requestGuidance(
        request: InterventionRequest,
        options: UIInteractionOptions = {}
    ): Promise<InterventionResponse> {
        // Show options if alternatives are provided
        if (request.alternatives && request.alternatives.length > 0) {
            const items = [
                { label: request.suggestedAction || 'Suggested action', isDefault: true },
                ...request.alternatives.map(alt => ({ label: alt, isDefault: false }))
            ];

            const selection = await vscode.window.showQuickPick(items, {
                placeHolder: request.description,
                canPickMany: false,
                ignoreFocusOut: !options.allowCancel
            });

            if (!selection) {
                // User cancelled
                return {
                    requestId: request.id,
                    timestamp: Date.now(),
                    approved: false,
                    feedback: 'User cancelled guidance request'
                };
            }

            const selectedIndex = selection.isDefault ? -1 : 
                request.alternatives!.indexOf(selection.label);

            return {
                requestId: request.id,
                timestamp: Date.now(),
                approved: true,
                selectedAlternative: selectedIndex,
                modifiedAction: selection.label
            };
        } else {
            // Request free-form guidance
            const guidance = await vscode.window.showInputBox({
                prompt: request.description,
                placeHolder: request.suggestedAction || 'Enter your guidance here',
                ignoreFocusOut: !options.allowCancel
            });

            if (guidance === undefined) {
                // User cancelled
                return {
                    requestId: request.id,
                    timestamp: Date.now(),
                    approved: false,
                    feedback: 'User cancelled guidance request'
                };
            }

            return {
                requestId: request.id,
                timestamp: Date.now(),
                approved: true,
                modifiedAction: guidance
            };
        }
    }

    /**
     * Request user to take over a task
     */
    async requestTakeover(
        request: InterventionRequest,
        options: UIInteractionOptions = {}
    ): Promise<InterventionResponse> {
        // Show takeover request
        const result = await vscode.window.showInformationMessage(
            `X10sion AI needs you to take over: ${request.description}`,
            { modal: options.modal || true },
            'Take Over',
            'Cancel'
        );

        if (result === 'Take Over') {
            // Open relevant files or UI for the user to take over
            if (request.context && request.context.filePath) {
                try {
                    const document = await vscode.workspace.openTextDocument(request.context.filePath);
                    await vscode.window.showTextDocument(document);
                } catch (error) {
                    console.warn('Failed to open file for takeover:', error);
                }
            }

            // Collect user feedback after takeover
            const feedback = await vscode.window.showInputBox({
                prompt: 'Please provide feedback on why you needed to take over',
                placeHolder: 'Enter feedback to help improve AI assistance'
            });

            return {
                requestId: request.id,
                timestamp: Date.now(),
                approved: false, // AI doesn't proceed since human took over
                feedback: feedback || 'User took over the task'
            };
        }

        // User cancelled takeover
        return {
            requestId: request.id,
            timestamp: Date.now(),
            approved: false,
            feedback: 'User cancelled takeover request'
        };
    }

    /**
     * Show detailed information about a request
     */
    private async showDetails(request: InterventionRequest): Promise<void> {
        let details = `Action Type: ${request.actionType}\n`;
        details += `Description: ${request.description}\n`;
        
        if (request.suggestedAction) {
            details += `Suggested Action: ${request.suggestedAction}\n`;
        }
        
        if (request.alternatives && request.alternatives.length > 0) {
            details += `Alternatives:\n${request.alternatives.map((alt, i) => `  ${i + 1}. ${alt}`).join('\n')}\n`;
        }
        
        if (request.context) {
            details += `Context: ${JSON.stringify(request.context, null, 2)}\n`;
        }

        await vscode.window.showInformationMessage(
            details,
            { modal: true },
            'OK'
        );
    }

    /**
     * Collect feedback from user
     */
    async collectFeedback(
        requestId: string,
        prompt: string = 'Please provide feedback',
        placeholder: string = 'Enter your feedback'
    ): Promise<string> {
        const feedback = await vscode.window.showInputBox({
            prompt,
            placeHolder: placeholder,
            ignoreFocusOut: true
        });

        return feedback || '';
    }

    /**
     * Show intervention status
     */
    async showStatus(pendingCount: number, recentApprovals: number, recentRejections: number): Promise<void> {
        const message = `X10sion Intervention Status:
Pending: ${pendingCount}
Recent Approvals: ${recentApprovals}
Recent Rejections: ${recentRejections}`;

        await vscode.window.showInformationMessage(message, 'OK');
    }

    /**
     * Show error message
     */
    async showError(message: string, error?: Error): Promise<void> {
        const fullMessage = error ? `${message}: ${error.message}` : message;
        await vscode.window.showErrorMessage(fullMessage, 'OK');
    }

    /**
     * Check if an action is low risk (helper method)
     */
    private isLowRiskAction(request: InterventionRequest): boolean {
        // This could be moved to a shared utility, but keeping it simple for now
        const lowRiskActions = ['code_generation', 'uncertain_output'];
        return lowRiskActions.includes(request.actionType);
    }

    /**
     * Update capabilities
     */
    updateCapabilities(capabilities: HumanInTheLoopCapabilities): void {
        this.capabilities = capabilities;
    }
}
