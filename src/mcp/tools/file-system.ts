/**
 * File System Tool for MCP
 *
 * This module provides a tool for performing file system operations.
 * Refactored for May 2025 optimization standards (300-500 lines per file).
 */

import * as vscode from 'vscode';
import {
    MCPTool,
    MCPComponentType,
    MCPComponentStatus,
    ToolId
} from '../types.js';
import { log } from '../utils.js';
import { HumanInTheLoopAgent, InterventionLevel } from '../../human-in-the-loop/agent.js';

// Import modular components
import {
    FileSystemOperationType,
    FileSystemOperationOptions,
    FileSystemOperationResult
} from './file-system/types.js';
import { executeReadOperation, checkExists, getStats } from './file-system/read-operations.js';
import { executeWriteOperation, executeAppendOperation } from './file-system/write-operations.js';
import { executeFileManagementOperation } from './file-system/file-management.js';
import { executeDirectoryOperation } from './file-system/directory-operations.js';
import { resolvePath, getInterventionLevel } from './file-system/utils.js';

// Re-export types for external use
export {
    FileSystemOperationType,
    FileSystemOperationOptions,
    FileSystemOperationResult
} from './file-system/types.js';

/**
 * File system operation options for MCP tool
 */
export interface FileSystemOptions {
    /** The type of operation to perform */
    operation: FileSystemOperationType;
    /** The path of the file or directory */
    path: string;
    /** The content to write (for write and append operations) */
    content?: string;
    /** The destination path (for copy, move, and rename operations) */
    destination?: string;
    /** The encoding to use (for read, write, and append operations) */
    encoding?: string;
    /** The intervention level for human-in-the-loop */
    interventionLevel?: InterventionLevel;
}

/**
 * File System Tool
 *
 * Provides a tool for performing file system operations.
 */
export const fileSystemTool: MCPTool = {
    id: 'file-system',
    name: 'File System',
    description: 'Performs file system operations',
    type: MCPComponentType.TOOL,
    version: '1.0.0',
    status: MCPComponentStatus.ACTIVE,
    parameters: [
        {
            name: 'operation',
            description: 'The type of operation to perform',
            type: 'string',
            required: true
        },
        {
            name: 'path',
            description: 'The path of the file or directory',
            type: 'string',
            required: true
        },
        {
            name: 'content',
            description: 'The content to write (for write and append operations)',
            type: 'string',
            required: false
        },
        {
            name: 'destination',
            description: 'The destination path (for copy, move, and rename operations)',
            type: 'string',
            required: false
        },
        {
            name: 'encoding',
            description: 'The encoding to use (for read, write, and append operations)',
            type: 'string',
            required: false
        },
        {
            name: 'interventionLevel',
            description: 'The intervention level for human-in-the-loop',
            type: 'string',
            required: false
        }
    ],

    /**
     * Execute a file system operation
     * @param options File system options
     * @returns Operation result
     */
    async execute(options: any): Promise<FileSystemOperationResult> {
        try {
            // Validate options
            if (!options || !options.operation) {
                throw new Error('Operation is required');
            }

            if (!options.path) {
                throw new Error('Path is required');
            }

            const operation = options.operation as FileSystemOperationType;
            const filePath = options.path;
            const interventionLevel = options.interventionLevel || getInterventionLevel(operation);

            // Get the human-in-the-loop agent
            const hitlAgent = HumanInTheLoopAgent.getInstance();

            // Request intervention if needed
            if (interventionLevel !== InterventionLevel.NONE) {
                const intervention = await hitlAgent.requestIntervention({
                    level: interventionLevel,
                    title: 'File System Operation',
                    message: `Do you want to perform the following operation?\n\nOperation: ${operation}\nPath: ${filePath}${options.destination ? `\nDestination: ${options.destination}` : ''}`,
                    actions: ['Execute', 'Cancel'],
                    defaultAction: 'Execute',
                    data: { operation, path: filePath, destination: options.destination }
                });

                // If the intervention was rejected, return an error
                if (intervention.rejected) {
                    return {
                        operation,
                        path: filePath,
                        success: false,
                        error: 'File system operation was rejected by the user'
                    };
                }
            }

            // Prepare operation options
            const operationOptions: FileSystemOperationOptions = {
                content: options.content,
                destination: options.destination,
                encoding: options.encoding as BufferEncoding,
                recursive: options.recursive,
                overwrite: options.overwrite
            };

            // Perform the operation using modular components
            switch (operation) {
                case FileSystemOperationType.READ:
                    return await executeReadOperation(filePath, operationOptions);

                case FileSystemOperationType.WRITE:
                    return await executeWriteOperation(filePath, operationOptions);

                case FileSystemOperationType.APPEND:
                    return await executeAppendOperation(filePath, operationOptions);

                case FileSystemOperationType.DELETE:
                case FileSystemOperationType.COPY:
                case FileSystemOperationType.MOVE:
                case FileSystemOperationType.RENAME:
                    return await executeFileManagementOperation(operation, filePath, operationOptions);

                case FileSystemOperationType.CREATE_DIRECTORY:
                case FileSystemOperationType.LIST_DIRECTORY:
                    return await executeDirectoryOperation(operation, filePath, operationOptions);

                case FileSystemOperationType.CHECK_EXISTS:
                    return await checkExists(filePath);

                case FileSystemOperationType.GET_STATS:
                    return await getStats(filePath);

                default:
                    throw new Error(`Unsupported operation: ${operation}`);
            }
        } catch (error) {
            log('error', 'Failed to perform file system operation', error);
            return {
                operation: options.operation,
                path: options.path,
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
};

/**
 * Register the file system tool with the MCP server
 * @param server MCP server
 * @returns Tool ID
 */
export async function registerFileSystemTool(server: any): Promise<ToolId> {
    try {
        const toolId = await server.registerTool(fileSystemTool);
        log('info', `File system tool registered: ${toolId}`);
        return toolId;
    } catch (error) {
        log('error', 'Failed to register file system tool', error);
        throw error;
    }
}
