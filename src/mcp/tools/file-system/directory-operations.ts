/**
 * File System Directory Operations
 *
 * Handles directory operations for the file system tool.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import * as fs from 'fs';
import * as path from 'path';
import { FileSystemOperationType, FileSystemOperationResult, FileSystemOperationOptions, DirectoryItem } from './types.js';
import { resolvePath, validatePath, formatFileSize } from './utils.js';

/**
 * Execute directory operation
 * @param operation Operation type
 * @param dirPath Directory path
 * @param options Operation options
 * @returns Operation result
 */
export async function executeDirectoryOperation(
    operation: FileSystemOperationType,
    dirPath: string,
    options: FileSystemOperationOptions = {}
): Promise<FileSystemOperationResult> {
    switch (operation) {
        case FileSystemOperationType.CREATE_DIRECTORY:
            return await createDirectory(dirPath, options);
        case FileSystemOperationType.LIST_DIRECTORY:
            return await listDirectory(dirPath, options);
        default:
            return {
                operation,
                path: dirPath,
                success: false,
                error: `Unsupported directory operation: ${operation}`
            };
    }
}

/**
 * Create a directory
 * @param dirPath Directory path
 * @param options Operation options
 * @returns Operation result
 */
export async function createDirectory(
    dirPath: string,
    options: FileSystemOperationOptions = {}
): Promise<FileSystemOperationResult> {
    const resolvedPath = resolvePath(dirPath);
    
    if (!validatePath(resolvedPath)) {
        return {
            operation: FileSystemOperationType.CREATE_DIRECTORY,
            path: dirPath,
            success: false,
            error: 'Invalid or unsafe directory path'
        };
    }

    try {
        await fs.promises.mkdir(resolvedPath, { 
            recursive: options.recursive !== false 
        });
        
        return {
            operation: FileSystemOperationType.CREATE_DIRECTORY,
            path: dirPath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.CREATE_DIRECTORY,
            path: dirPath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * List directory contents
 * @param dirPath Directory path
 * @param options Operation options
 * @returns Operation result
 */
export async function listDirectory(
    dirPath: string,
    options: FileSystemOperationOptions = {}
): Promise<FileSystemOperationResult> {
    const resolvedPath = resolvePath(dirPath);
    
    if (!validatePath(resolvedPath)) {
        return {
            operation: FileSystemOperationType.LIST_DIRECTORY,
            path: dirPath,
            success: false,
            error: 'Invalid or unsafe directory path'
        };
    }

    try {
        const entries = await fs.promises.readdir(resolvedPath, { withFileTypes: true });
        const items: DirectoryItem[] = [];
        
        for (const entry of entries) {
            const itemPath = path.join(resolvedPath, entry.name);
            let stats: fs.Stats | undefined;
            
            try {
                stats = await fs.promises.stat(itemPath);
            } catch (error) {
                // Skip items we can't stat
                continue;
            }
            
            items.push({
                name: entry.name,
                path: path.relative(resolvedPath, itemPath),
                isFile: entry.isFile(),
                isDirectory: entry.isDirectory(),
                size: stats.size,
                mtime: stats.mtime
            });
        }
        
        // Sort items: directories first, then files, both alphabetically
        items.sort((a, b) => {
            if (a.isDirectory && !b.isDirectory) return -1;
            if (!a.isDirectory && b.isDirectory) return 1;
            return a.name.localeCompare(b.name);
        });
        
        return {
            operation: FileSystemOperationType.LIST_DIRECTORY,
            path: dirPath,
            success: true,
            files: items.map(item => {
                const sizeStr = item.size ? formatFileSize(item.size) : '';
                const typeStr = item.isDirectory ? 'DIR' : 'FILE';
                const mtimeStr = item.mtime ? item.mtime.toISOString().split('T')[0] : '';
                return `${typeStr.padEnd(4)} ${sizeStr.padEnd(8)} ${mtimeStr.padEnd(10)} ${item.name}`;
            })
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.LIST_DIRECTORY,
            path: dirPath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
