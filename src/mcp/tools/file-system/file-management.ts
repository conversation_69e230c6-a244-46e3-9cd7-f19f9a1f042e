/**
 * File System File Management Operations
 *
 * Handles file management operations (copy, move, rename, delete) for the file system tool.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import * as fs from 'fs';
import * as path from 'path';
import { FileSystemOperationType, FileSystemOperationResult, FileSystemOperationOptions } from './types.js';
import { resolvePath, validatePath } from './utils.js';

/**
 * Execute file management operation
 * @param operation Operation type
 * @param filePath File path
 * @param options Operation options
 * @returns Operation result
 */
export async function executeFileManagementOperation(
    operation: FileSystemOperationType,
    filePath: string,
    options: FileSystemOperationOptions = {}
): Promise<FileSystemOperationResult> {
    switch (operation) {
        case FileSystemOperationType.DELETE:
            return await deleteFile(filePath);
        case FileSystemOperationType.COPY:
            return await copyFile(filePath, options.destination!);
        case FileSystemOperationType.MOVE:
        case FileSystemOperationType.RENAME:
            return await moveFile(filePath, options.destination!);
        default:
            return {
                operation,
                path: filePath,
                success: false,
                error: `Unsupported file management operation: ${operation}`
            };
    }
}

/**
 * Delete a file
 * @param filePath File path
 * @returns Operation result
 */
export async function deleteFile(filePath: string): Promise<FileSystemOperationResult> {
    const resolvedPath = resolvePath(filePath);
    
    if (!validatePath(resolvedPath)) {
        return {
            operation: FileSystemOperationType.DELETE,
            path: filePath,
            success: false,
            error: 'Invalid or unsafe file path'
        };
    }

    try {
        const stats = await fs.promises.stat(resolvedPath);
        
        if (stats.isDirectory()) {
            await fs.promises.rmdir(resolvedPath, { recursive: true });
        } else {
            await fs.promises.unlink(resolvedPath);
        }
        
        return {
            operation: FileSystemOperationType.DELETE,
            path: filePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.DELETE,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Copy a file
 * @param sourcePath Source file path
 * @param destinationPath Destination file path
 * @returns Operation result
 */
export async function copyFile(sourcePath: string, destinationPath: string): Promise<FileSystemOperationResult> {
    const resolvedSource = resolvePath(sourcePath);
    const resolvedDestination = resolvePath(destinationPath);
    
    if (!validatePath(resolvedSource) || !validatePath(resolvedDestination)) {
        return {
            operation: FileSystemOperationType.COPY,
            path: sourcePath,
            success: false,
            error: 'Invalid or unsafe file path'
        };
    }

    try {
        // Ensure destination directory exists
        const destDir = path.dirname(resolvedDestination);
        await fs.promises.mkdir(destDir, { recursive: true });

        await fs.promises.copyFile(resolvedSource, resolvedDestination);
        
        return {
            operation: FileSystemOperationType.COPY,
            path: sourcePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.COPY,
            path: sourcePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Move/rename a file
 * @param sourcePath Source file path
 * @param destinationPath Destination file path
 * @returns Operation result
 */
export async function moveFile(sourcePath: string, destinationPath: string): Promise<FileSystemOperationResult> {
    const resolvedSource = resolvePath(sourcePath);
    const resolvedDestination = resolvePath(destinationPath);
    
    if (!validatePath(resolvedSource) || !validatePath(resolvedDestination)) {
        return {
            operation: FileSystemOperationType.MOVE,
            path: sourcePath,
            success: false,
            error: 'Invalid or unsafe file path'
        };
    }

    try {
        // Ensure destination directory exists
        const destDir = path.dirname(resolvedDestination);
        await fs.promises.mkdir(destDir, { recursive: true });

        await fs.promises.rename(resolvedSource, resolvedDestination);
        
        return {
            operation: FileSystemOperationType.MOVE,
            path: sourcePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.MOVE,
            path: sourcePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
