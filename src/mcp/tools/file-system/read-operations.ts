/**
 * File System Read Operations
 *
 * Handles file reading operations for the file system tool.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import * as fs from 'fs';
import * as path from 'path';
import { FileSystemOperationType, FileSystemOperationResult, FileSystemOperationOptions } from './types.js';
import { resolvePath, getSafeEncoding, validatePath } from './utils.js';

/**
 * Execute read operation
 * @param filePath File path
 * @param options Operation options
 * @returns Operation result
 */
export async function executeReadOperation(
    filePath: string,
    options: FileSystemOperationOptions = {}
): Promise<FileSystemOperationResult> {
    const resolvedPath = resolvePath(filePath);
    
    if (!validatePath(resolvedPath)) {
        return {
            operation: FileSystemOperationType.READ,
            path: filePath,
            success: false,
            error: 'Invalid or unsafe file path'
        };
    }

    try {
        const encoding = getSafeEncoding(options.encoding);
        const content = await fs.promises.readFile(resolvedPath, encoding);
        
        return {
            operation: FileSystemOperationType.READ,
            path: filePath,
            success: true,
            content: content.toString()
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.READ,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Check if file exists
 * @param filePath File path
 * @returns Operation result
 */
export async function checkExists(filePath: string): Promise<FileSystemOperationResult> {
    const resolvedPath = resolvePath(filePath);
    
    if (!validatePath(resolvedPath)) {
        return {
            operation: FileSystemOperationType.CHECK_EXISTS,
            path: filePath,
            success: false,
            error: 'Invalid or unsafe file path'
        };
    }

    try {
        await fs.promises.access(resolvedPath);
        return {
            operation: FileSystemOperationType.CHECK_EXISTS,
            path: filePath,
            success: true,
            exists: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.CHECK_EXISTS,
            path: filePath,
            success: true,
            exists: false
        };
    }
}

/**
 * Get file stats
 * @param filePath File path
 * @returns Operation result
 */
export async function getStats(filePath: string): Promise<FileSystemOperationResult> {
    const resolvedPath = resolvePath(filePath);
    
    if (!validatePath(resolvedPath)) {
        return {
            operation: FileSystemOperationType.GET_STATS,
            path: filePath,
            success: false,
            error: 'Invalid or unsafe file path'
        };
    }

    try {
        const stats = await fs.promises.stat(resolvedPath);
        
        return {
            operation: FileSystemOperationType.GET_STATS,
            path: filePath,
            success: true,
            stats: {
                isFile: stats.isFile(),
                isDirectory: stats.isDirectory(),
                size: stats.size,
                mtime: stats.mtime,
                ctime: stats.ctime,
                atime: stats.atime
            }
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.GET_STATS,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
