/**
 * File System Tool Types
 *
 * Type definitions for file system operations.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

/**
 * File system operation types
 */
export enum FileSystemOperationType {
    READ = 'read',
    WRITE = 'write',
    APPEND = 'append',
    DELETE = 'delete',
    COPY = 'copy',
    MOVE = 'move',
    RENAME = 'rename',
    CREATE_DIRECTORY = 'create_directory',
    LIST_DIRECTORY = 'list_directory',
    CHECK_EXISTS = 'check_exists',
    GET_STATS = 'get_stats'
}

/**
 * File system operation options
 */
export interface FileSystemOperationOptions {
    content?: string;
    destination?: string;
    encoding?: BufferEncoding;
    recursive?: boolean;
    overwrite?: boolean;
}

/**
 * File system operation result
 */
export interface FileSystemOperationResult {
    operation: FileSystemOperationType;
    path: string;
    success: boolean;
    content?: string;
    stats?: any;
    files?: string[];
    exists?: boolean;
    error?: string;
}

/**
 * File stats interface
 */
export interface FileStats {
    isFile: boolean;
    isDirectory: boolean;
    size: number;
    mtime: Date;
    ctime: Date;
    atime: Date;
}

/**
 * Directory listing item
 */
export interface DirectoryItem {
    name: string;
    path: string;
    isFile: boolean;
    isDirectory: boolean;
    size?: number;
    mtime?: Date;
}
