/**
 * File System Tool Utilities
 *
 * Utility functions for file system operations.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { InterventionLevel } from '../../../human-in-the-loop/agent.js';
import { FileSystemOperationType } from './types.js';

/**
 * Resolve a path relative to the workspace root
 * @param filePath File path
 * @returns Resolved path
 */
export function resolvePath(filePath: string): string {
    if (path.isAbsolute(filePath)) {
        return filePath;
    }

    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error('No workspace folder is open');
    }

    return path.join(workspaceFolders[0].uri.fsPath, filePath);
}

/**
 * Get intervention level for a file system operation
 * @param operation Operation type
 * @returns Intervention level
 */
export function getInterventionLevel(operation: FileSystemOperationType): InterventionLevel {
    switch (operation) {
        case FileSystemOperationType.READ:
        case FileSystemOperationType.LIST_DIRECTORY:
        case FileSystemOperationType.CHECK_EXISTS:
        case FileSystemOperationType.GET_STATS:
            return InterventionLevel.NONE;
        
        case FileSystemOperationType.WRITE:
        case FileSystemOperationType.APPEND:
        case FileSystemOperationType.CREATE_DIRECTORY:
            return InterventionLevel.NOTIFICATION;
        
        case FileSystemOperationType.DELETE:
        case FileSystemOperationType.MOVE:
        case FileSystemOperationType.RENAME:
            return InterventionLevel.APPROVAL;
        
        case FileSystemOperationType.COPY:
            return InterventionLevel.NOTIFICATION;
        
        default:
            return InterventionLevel.APPROVAL;
    }
}

/**
 * Validate file path
 * @param filePath File path to validate
 * @returns True if valid, false otherwise
 */
export function validatePath(filePath: string): boolean {
    if (!filePath || typeof filePath !== 'string') {
        return false;
    }

    // Check for dangerous path patterns
    const dangerousPatterns = [
        /\.\./,  // Parent directory traversal
        /^\/etc/,  // System directories
        /^\/usr/,
        /^\/bin/,
        /^\/sbin/,
        /^\/var/,
        /^\/tmp/,
        /^C:\\Windows/i,  // Windows system directories
        /^C:\\Program Files/i,
        /^C:\\System/i
    ];

    return !dangerousPatterns.some(pattern => pattern.test(filePath));
}

/**
 * Get safe file encoding
 * @param encoding Requested encoding
 * @returns Safe encoding
 */
export function getSafeEncoding(encoding?: BufferEncoding): BufferEncoding {
    const safeEncodings: BufferEncoding[] = ['utf8', 'utf-8', 'ascii', 'base64', 'hex'];
    
    if (encoding && safeEncodings.includes(encoding)) {
        return encoding;
    }
    
    return 'utf8';
}

/**
 * Format file size for display
 * @param bytes File size in bytes
 * @returns Formatted size string
 */
export function formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * Check if path is within workspace
 * @param filePath File path to check
 * @returns True if within workspace, false otherwise
 */
export function isWithinWorkspace(filePath: string): boolean {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
        return false;
    }

    const resolvedPath = path.resolve(filePath);
    return workspaceFolders.some(folder => {
        const workspacePath = folder.uri.fsPath;
        return resolvedPath.startsWith(path.resolve(workspacePath));
    });
}
