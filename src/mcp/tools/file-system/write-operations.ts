/**
 * File System Write Operations
 *
 * Handles file writing operations for the file system tool.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import * as fs from 'fs';
import * as path from 'path';
import { FileSystemOperationType, FileSystemOperationResult, FileSystemOperationOptions } from './types.js';
import { resolvePath, getSafeEncoding, validatePath } from './utils.js';

/**
 * Execute write operation
 * @param filePath File path
 * @param options Operation options
 * @returns Operation result
 */
export async function executeWriteOperation(
    filePath: string,
    options: FileSystemOperationOptions = {}
): Promise<FileSystemOperationResult> {
    const resolvedPath = resolvePath(filePath);
    
    if (!validatePath(resolvedPath)) {
        return {
            operation: FileSystemOperationType.WRITE,
            path: filePath,
            success: false,
            error: 'Invalid or unsafe file path'
        };
    }

    if (!options.content) {
        return {
            operation: FileSystemOperationType.WRITE,
            path: filePath,
            success: false,
            error: 'Content is required for write operation'
        };
    }

    try {
        // Ensure directory exists
        const dir = path.dirname(resolvedPath);
        await fs.promises.mkdir(dir, { recursive: true });

        const encoding = getSafeEncoding(options.encoding);
        await fs.promises.writeFile(resolvedPath, options.content, encoding);
        
        return {
            operation: FileSystemOperationType.WRITE,
            path: filePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.WRITE,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Execute append operation
 * @param filePath File path
 * @param options Operation options
 * @returns Operation result
 */
export async function executeAppendOperation(
    filePath: string,
    options: FileSystemOperationOptions = {}
): Promise<FileSystemOperationResult> {
    const resolvedPath = resolvePath(filePath);
    
    if (!validatePath(resolvedPath)) {
        return {
            operation: FileSystemOperationType.APPEND,
            path: filePath,
            success: false,
            error: 'Invalid or unsafe file path'
        };
    }

    if (!options.content) {
        return {
            operation: FileSystemOperationType.APPEND,
            path: filePath,
            success: false,
            error: 'Content is required for append operation'
        };
    }

    try {
        // Ensure directory exists
        const dir = path.dirname(resolvedPath);
        await fs.promises.mkdir(dir, { recursive: true });

        const encoding = getSafeEncoding(options.encoding);
        await fs.promises.appendFile(resolvedPath, options.content, encoding);
        
        return {
            operation: FileSystemOperationType.APPEND,
            path: filePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.APPEND,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
