/**
 * LLM Monitor Data Manager
 *
 * Handles data storage and management for the LLM monitor.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import {
    LLMRequest,
    LLMResponse,
    LLMError,
    LLMIssue,
    LLMIntervention,
    LLMMonitorOptions
} from './types.js';

/**
 * Data Manager for LLM Monitor
 * 
 * Manages all data storage, retrieval, and history management
 */
export class DataManager {
    private requests: LLMRequest[] = [];
    private responses: LLMResponse[] = [];
    private errors: LLMError[] = [];
    private issues: LLMIssue[] = [];
    private interventions: LLMIntervention[] = [];
    private options: LLMMonitorOptions;

    constructor(options: LLMMonitorOptions) {
        this.options = options;
    }

    /**
     * Add a request to the data store
     * @param request Request to add
     */
    addRequest(request: LLMRequest): void {
        this.requests.push(request);
        this.trimHistory('requests');
    }

    /**
     * Add a response to the data store
     * @param response Response to add
     */
    addResponse(response: LLMResponse): void {
        this.responses.push(response);
        this.trimHistory('responses');
    }

    /**
     * Add an error to the data store
     * @param error Error to add
     */
    addError(error: LLMError): void {
        this.errors.push(error);
        this.trimHistory('errors');
    }

    /**
     * Add issues to the data store
     * @param issues Issues to add
     */
    addIssues(issues: LLMIssue[]): void {
        this.issues.push(...issues);
        this.trimHistory('issues');
    }

    /**
     * Add interventions to the data store
     * @param interventions Interventions to add
     */
    addInterventions(interventions: LLMIntervention[]): void {
        this.interventions.push(...interventions);
        this.trimHistory('interventions');
    }

    /**
     * Get a request by ID
     * @param requestId Request ID
     * @returns Request or undefined
     */
    getRequest(requestId: string): LLMRequest | undefined {
        return this.requests.find(req => req.id === requestId);
    }

    /**
     * Get all requests
     * @returns Array of all requests
     */
    getAllRequests(): LLMRequest[] {
        return [...this.requests];
    }

    /**
     * Get all responses
     * @returns Array of all responses
     */
    getAllResponses(): LLMResponse[] {
        return [...this.responses];
    }

    /**
     * Get all errors
     * @returns Array of all errors
     */
    getAllErrors(): LLMError[] {
        return [...this.errors];
    }

    /**
     * Get all issues
     * @returns Array of all issues
     */
    getAllIssues(): LLMIssue[] {
        return [...this.issues];
    }

    /**
     * Get all interventions
     * @returns Array of all interventions
     */
    getAllInterventions(): LLMIntervention[] {
        return [...this.interventions];
    }

    /**
     * Get interventions for a specific issue
     * @param issueId Issue ID
     * @returns Array of interventions
     */
    getInterventionsForIssue(issueId: string): LLMIntervention[] {
        return this.interventions.filter(int => int.issueId === issueId);
    }

    /**
     * Get requests for a specific agent
     * @param agentId Agent ID
     * @returns Array of requests
     */
    getRequestsForAgent(agentId: string): LLMRequest[] {
        return this.requests.filter(req => req.agentId === agentId);
    }

    /**
     * Get responses for a specific agent
     * @param agentId Agent ID
     * @returns Array of responses
     */
    getResponsesForAgent(agentId: string): LLMResponse[] {
        return this.responses.filter(res => res.agentId === agentId);
    }

    /**
     * Get errors for a specific agent
     * @param agentId Agent ID
     * @returns Array of errors
     */
    getErrorsForAgent(agentId: string): LLMError[] {
        return this.errors.filter(err => err.agentId === agentId);
    }

    /**
     * Get issues for a specific agent
     * @param agentId Agent ID
     * @returns Array of issues
     */
    getIssuesForAgent(agentId: string): LLMIssue[] {
        return this.issues.filter(issue => issue.agentId === agentId);
    }

    /**
     * Load data from external source
     * @param data Data to load
     */
    loadData(data: {
        requests: LLMRequest[];
        responses: LLMResponse[];
        errors: LLMError[];
        issues: LLMIssue[];
        interventions: LLMIntervention[];
    }): void {
        this.requests = data.requests || [];
        this.responses = data.responses || [];
        this.errors = data.errors || [];
        this.issues = data.issues || [];
        this.interventions = data.interventions || [];
    }

    /**
     * Get all data for persistence
     * @returns All data
     */
    getAllData(): {
        requests: LLMRequest[];
        responses: LLMResponse[];
        errors: LLMError[];
        issues: LLMIssue[];
        interventions: LLMIntervention[];
    } {
        return {
            requests: [...this.requests],
            responses: [...this.responses],
            errors: [...this.errors],
            issues: [...this.issues],
            interventions: [...this.interventions]
        };
    }

    /**
     * Clear all data
     */
    clear(): void {
        this.requests = [];
        this.responses = [];
        this.errors = [];
        this.issues = [];
        this.interventions = [];
    }

    /**
     * Trim history for a specific data type
     * @param dataType Type of data to trim
     */
    private trimHistory(dataType: 'requests' | 'responses' | 'errors' | 'issues' | 'interventions'): void {
        const maxItems = this.options.maxHistoryItems || 1000;
        
        switch (dataType) {
            case 'requests':
                if (this.requests.length > maxItems) {
                    this.requests.shift();
                }
                break;
            case 'responses':
                if (this.responses.length > maxItems) {
                    this.responses.shift();
                }
                break;
            case 'errors':
                if (this.errors.length > maxItems) {
                    this.errors.shift();
                }
                break;
            case 'issues':
                if (this.issues.length > maxItems) {
                    this.issues.shift();
                }
                break;
            case 'interventions':
                if (this.interventions.length > maxItems) {
                    this.interventions.shift();
                }
                break;
        }
    }
}
