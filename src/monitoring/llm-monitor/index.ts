/**
 * LLM Monitor for X10sion
 *
 * This module provides monitoring capabilities for language model interactions,
 * including tracking usage, performance, and potential issues.
 */

import * as vscode from 'vscode';
import { BaseAgent, AgentStatus } from '../../agents/base-agent.js';
import { IssueDetectionService } from './issue-detection.js';
import { InterventionService } from './interventions.js';
import { MetricsService } from './metrics.js';
import { PersistenceService } from './persistence.js';
import { EventHandlerService } from './event-handler.js';
import { AgentManagerService } from './agent-manager.js';
import { DataManager } from './data-manager.js';
import { TrackingService } from './tracking-service.js';
import { StatusManager } from './status-manager.js';
import {
    LLMRequest,
    LLMResponse,
    LLMError,
    LLMIssue,
    LLMIntervention,
    LLMUsageStats,
    LLMMonitorOptions,
    LLMIssueType,
    InterventionStrategy,
    AgentBehaviorAnalysis
} from './types.js';

// Re-export types for external use
export {
    LLMRequest,
    LLMResponse,
    LLMError,
    LLMIssue,
    LLMIntervention,
    LLMUsageStats,
    LLMMonitorOptions,
    LLMIssueType,
    InterventionStrategy,
    AgentBehaviorAnalysis
};

/**
 * LLM Monitor class
 *
 * Monitors language model interactions for performance, usage, and issues.
 * Refactored for May 2025 optimization standards (300-500 lines per file).
 */
export class LLMMonitor {
    private options: LLMMonitorOptions;
    private disposables: vscode.Disposable[] = [];

    // Core services
    private dataManager: DataManager;
    private trackingService: TrackingService;
    private statusManager: StatusManager;
    private issueDetectionService: IssueDetectionService;
    private interventionService: InterventionService;
    private metricsService: MetricsService;
    private persistenceService: PersistenceService;
    private eventHandlerService: EventHandlerService;
    private agentManagerService: AgentManagerService;

    constructor(options: LLMMonitorOptions = {}) {
        this.options = {
            maxHistoryItems: 1000,
            enableIssueDetection: true,
            issueDetectionConfidenceThreshold: 0.7,
            enableTokenCounting: true,
            enablePersistence: false,
            persistencePath: '',
            debugMode: false,
            enableInterventions: true,
            interventionStrategies: [
                InterventionStrategy.RETRY,
                InterventionStrategy.REFINE_PROMPT,
                InterventionStrategy.SWITCH_MODEL,
                InterventionStrategy.FALLBACK
            ],
            maxRetries: 3,
            smallModelThreshold: 8192, // 8K context window is considered small
            ...options
        };

        console.log('LLM Monitor initialized with options:', this.options);

        // Initialize core services
        this.dataManager = new DataManager(this.options);
        this.statusManager = new StatusManager();
        this.eventHandlerService = new EventHandlerService();
        this.issueDetectionService = new IssueDetectionService();
        this.interventionService = new InterventionService(
            this.options,
            this.statusManager.getStatusBarItem(),
            this.eventHandlerService
        );
        this.metricsService = new MetricsService();
        this.persistenceService = new PersistenceService(this.options);
        this.agentManagerService = new AgentManagerService(
            this.metricsService,
            this.interventionService,
            this.options
        );
        this.trackingService = new TrackingService(
            this.dataManager,
            this.eventHandlerService,
            this.options
        );

        // Add status manager disposables to our disposables
        this.disposables.push(...this.statusManager.getDisposables());

        // Load persisted data if enabled
        if (this.options.enablePersistence && this.options.persistencePath) {
            const persistedData = this.persistenceService.loadPersistedData();
            this.dataManager.loadData(persistedData);
        }
    }

    /**
     * Register an agent with the monitor
     * @param agent Agent to register
     */
    registerAgent(agent: BaseAgent): void {
        this.agentManagerService.registerAgent(agent);
    }

    /**
     * Unregister an agent from the monitor
     * @param agent Agent to unregister
     */
    unregisterAgent(agent: BaseAgent): void {
        this.agentManagerService.unregisterAgent(agent);
    }

    /**
     * Track a language model request
     * @param modelName Model name
     * @param prompt Prompt text
     * @param options Request options
     * @param agentId Optional agent ID
     * @param metadata Optional metadata
     * @returns Request ID
     */
    trackRequest(
        modelName: string,
        prompt: string,
        options: any,
        agentId?: string,
        metadata?: Record<string, any>
    ): string {
        return this.trackingService.trackRequest(modelName, prompt, options, agentId, metadata);
    }

    /**
     * Track a language model response
     * @param requestId Request ID
     * @param modelName Model name
     * @param response Response text
     * @param latencyMs Latency in milliseconds
     * @param tokenCount Optional token count
     * @param agentId Optional agent ID
     * @param metadata Optional metadata
     * @returns Response ID
     */
    trackResponse(
        requestId: string,
        modelName: string,
        response: string,
        latencyMs: number,
        tokenCount?: number,
        agentId?: string,
        metadata?: Record<string, any>
    ): string {
        const responseId = this.trackingService.trackResponse(
            requestId, modelName, response, latencyMs, tokenCount, agentId, metadata
        );

        // Detect issues if enabled
        if (this.options.enableIssueDetection) {
            // Find the corresponding request
            const request = this.trackingService.getRequestForResponse(requestId);

            // Detect issues with request context if available
            const newIssues = this.issueDetectionService.detectIssues(response, responseId, request);

            if (newIssues.length > 0) {
                // Add issues to data manager
                this.dataManager.addIssues(newIssues);

                // Apply interventions if enabled
                if (this.options.enableInterventions && request) {
                    const existingInterventions = this.dataManager.getAllInterventions();
                    const newInterventions = this.interventionService.applyInterventions(
                        newIssues,
                        request,
                        existingInterventions
                    );

                    // Add interventions to data manager
                    this.dataManager.addInterventions(newInterventions);
                }

                // Update status bar to show issues
                this.statusManager.updateStatusBar(newIssues.length);

                // Emit issue events
                for (const issue of newIssues) {
                    this.eventHandlerService.emitIssueEvent(issue);
                }
            }
        }

        return responseId;
    }

    /**
     * Track an error in language model interaction
     * @param requestId Request ID
     * @param modelName Model name
     * @param error Error object
     * @param agentId Optional agent ID
     * @param metadata Optional metadata
     * @returns Error ID
     */
    trackError(
        requestId: string,
        modelName: string,
        error: Error,
        agentId?: string,
        metadata?: Record<string, any>
    ): string {
        return this.trackingService.trackError(requestId, modelName, error, agentId, metadata);
    }

    /**
     * Get usage statistics
     * @returns Usage statistics
     */
    getUsageStats(): LLMUsageStats {
        return this.metricsService.calculateUsageStats(
            this.dataManager.getAllRequests(),
            this.dataManager.getAllResponses(),
            this.dataManager.getAllErrors(),
            this.dataManager.getAllIssues()
        );
    }

    /**
     * Get agent status
     * @returns Map of agent IDs to their status
     */
    getAgentStatus(): Map<string, AgentStatus> {
        return this.agentManagerService.getAgentStatus();
    }

    /**
     * Subscribe to monitor events
     * @param listener Event listener
     * @returns Disposable
     */
    onEvent(listener: (event: any) => void): vscode.Disposable {
        return this.eventHandlerService.onEvent(listener);
    }

    /**
     * Apply agent-specific intervention
     * @param agentId Agent ID
     * @param issueType Type of issue to address
     * @param description Description of the issue
     * @returns Intervention ID if applied, undefined otherwise
     */
    applyAgentIntervention(agentId: string, issueType: LLMIssueType, description: string): string | undefined {
        // Check if agent exists and interventions are enabled
        if (!this.agentManagerService.hasAgent(agentId) || !this.options.enableInterventions) {
            return undefined;
        }

        // Get previous interventions for this agent
        const allIssues = this.dataManager.getAllIssues();
        const allInterventions = this.dataManager.getAllInterventions();
        const previousInterventions = allInterventions.filter(
            int => allIssues.find(i => i.id === int.issueId)?.agentId === agentId
        );

        // Apply intervention
        const intervention = this.agentManagerService.applyAgentIntervention(
            agentId,
            issueType,
            description,
            previousInterventions
        );

        if (intervention) {
            // Add to data manager
            this.dataManager.addInterventions([intervention]);
            return intervention.id;
        }

        return undefined;
    }

    /**
     * Get interventions for a specific issue
     * @param issueId Issue ID
     * @returns Array of interventions
     */
    getInterventionsForIssue(issueId: string): LLMIntervention[] {
        return this.dataManager.getInterventionsForIssue(issueId);
    }

    /**
     * Get all interventions
     * @returns Array of all interventions
     */
    getAllInterventions(): LLMIntervention[] {
        return this.dataManager.getAllInterventions();
    }

    /**
     * Get all issues
     * @returns Array of all issues
     */
    getAllIssues(): LLMIssue[] {
        return this.dataManager.getAllIssues();
    }

    /**
     * Get agent-specific usage statistics
     * @param agentId Agent ID
     * @returns Agent-specific usage statistics
     */
    getAgentStats(agentId: string): LLMUsageStats | undefined {
        // Check if agent exists
        if (!this.agentManagerService.hasAgent(agentId)) {
            return undefined;
        }

        return this.metricsService.calculateAgentStats(
            agentId,
            this.dataManager.getAllRequests(),
            this.dataManager.getAllResponses(),
            this.dataManager.getAllErrors(),
            this.dataManager.getAllIssues()
        );
    }

    /**
     * Analyze agent behavior patterns
     * @param agentId Agent ID
     * @returns Analysis of agent behavior patterns
     */
    analyzeAgentBehavior(agentId: string): AgentBehaviorAnalysis {
        // Check if agent exists
        if (!this.agentManagerService.hasAgent(agentId)) {
            return { patterns: [], anomalies: [], recommendations: [] };
        }

        return this.metricsService.analyzeAgentBehavior(
            agentId,
            this.dataManager.getAllResponses(),
            this.dataManager.getAllIssues(),
            this.dataManager.getAllErrors()
        );
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        // Save data if persistence is enabled
        if (this.options.enablePersistence && this.options.persistencePath) {
            const allData = this.dataManager.getAllData();
            this.persistenceService.saveData(
                allData.requests,
                allData.responses,
                allData.errors,
                allData.issues,
                allData.interventions
            );
        }

        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        // Dispose of services
        this.statusManager.dispose();
        this.eventHandlerService.dispose();
        this.agentManagerService.clear();

        // Clear data
        this.dataManager.clear();

        console.log('LLM Monitor disposed');
    }
}
