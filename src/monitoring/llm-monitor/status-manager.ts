/**
 * LLM Monitor Status Manager
 *
 * Handles status bar updates and UI feedback.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import * as vscode from 'vscode';

/**
 * Status Manager for LLM Monitor
 * 
 * Manages status bar updates and visual feedback
 */
export class StatusManager {
    private statusBarItem: vscode.StatusBarItem;
    private disposables: vscode.Disposable[] = [];

    constructor() {
        // Create status bar item
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this.statusBarItem.text = '$(eye) LLM Monitor';
        this.statusBarItem.tooltip = 'LLM Monitor is active';
        this.statusBarItem.command = 'x10sion.showLLMMonitorStats';
        this.statusBarItem.show();
        this.disposables.push(this.statusBarItem);
    }

    /**
     * Update the status bar with issue count
     * @param issueCount Number of issues
     */
    updateStatusBar(issueCount: number): void {
        if (issueCount > 0) {
            this.statusBarItem.text = `$(alert) LLM Monitor: ${issueCount} issue${issueCount > 1 ? 's' : ''}`;
            this.statusBarItem.tooltip = `${issueCount} LLM issue${issueCount > 1 ? 's' : ''} detected`;

            // Reset after a delay
            setTimeout(() => {
                this.statusBarItem.text = '$(eye) LLM Monitor';
                this.statusBarItem.tooltip = 'LLM Monitor is active';
            }, 10000);
        }
    }

    /**
     * Update status bar with custom text
     * @param text Custom text
     * @param tooltip Custom tooltip
     */
    updateCustomStatus(text: string, tooltip?: string): void {
        this.statusBarItem.text = text;
        if (tooltip) {
            this.statusBarItem.tooltip = tooltip;
        }
    }

    /**
     * Reset status bar to default
     */
    resetStatus(): void {
        this.statusBarItem.text = '$(eye) LLM Monitor';
        this.statusBarItem.tooltip = 'LLM Monitor is active';
    }

    /**
     * Show status bar item
     */
    show(): void {
        this.statusBarItem.show();
    }

    /**
     * Hide status bar item
     */
    hide(): void {
        this.statusBarItem.hide();
    }

    /**
     * Get the status bar item for external access
     * @returns Status bar item
     */
    getStatusBarItem(): vscode.StatusBarItem {
        return this.statusBarItem;
    }

    /**
     * Get disposables for cleanup
     * @returns Array of disposables
     */
    getDisposables(): vscode.Disposable[] {
        return [...this.disposables];
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }
}
