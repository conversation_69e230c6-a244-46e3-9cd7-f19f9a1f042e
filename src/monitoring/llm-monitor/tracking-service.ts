/**
 * LLM Monitor Tracking Service
 *
 * Handles tracking of requests, responses, and errors.
 * Part of the May 2025 refactoring for optimal file sizes.
 */

import {
    LLMRequest,
    LLMResponse,
    LLMError,
    LLMMonitorOptions
} from './types.js';
import { DataManager } from './data-manager.js';
import { EventHandlerService } from './event-handler.js';

/**
 * Tracking Service for LLM Monitor
 * 
 * Handles the creation and tracking of requests, responses, and errors
 */
export class TrackingService {
    private nextId: number = 0;
    private dataManager: DataManager;
    private eventHandlerService: EventHandlerService;
    private options: LLMMonitorOptions;

    constructor(
        dataManager: DataManager,
        eventHandlerService: EventHandlerService,
        options: LLMMonitorOptions
    ) {
        this.dataManager = dataManager;
        this.eventHandlerService = eventHandlerService;
        this.options = options;
    }

    /**
     * Track a language model request
     * @param modelName Model name
     * @param prompt Prompt text
     * @param options Request options
     * @param agentId Optional agent ID
     * @param metadata Optional metadata
     * @returns Request ID
     */
    trackRequest(
        modelName: string,
        prompt: string,
        options: any,
        agentId?: string,
        metadata?: Record<string, any>
    ): string {
        const id = `req-${Date.now()}-${this.nextId++}`;

        const request: LLMRequest = {
            id,
            timestamp: Date.now(),
            modelName,
            prompt,
            options,
            agentId,
            metadata
        };

        this.dataManager.addRequest(request);

        // Emit event
        this.eventHandlerService.emitRequestEvent(request);

        if (this.options.debugMode) {
            console.log(`LLM request tracked: ${modelName}`, {
                id,
                agentId,
                promptLength: prompt.length
            });
        }

        return id;
    }

    /**
     * Track a language model response
     * @param requestId Request ID
     * @param modelName Model name
     * @param response Response text
     * @param latencyMs Latency in milliseconds
     * @param tokenCount Optional token count
     * @param agentId Optional agent ID
     * @param metadata Optional metadata
     * @returns Response ID
     */
    trackResponse(
        requestId: string,
        modelName: string,
        response: string,
        latencyMs: number,
        tokenCount?: number,
        agentId?: string,
        metadata?: Record<string, any>
    ): string {
        const id = `res-${Date.now()}-${this.nextId++}`;

        const llmResponse: LLMResponse = {
            id,
            requestId,
            timestamp: Date.now(),
            modelName,
            response,
            latencyMs,
            tokenCount,
            agentId,
            metadata
        };

        this.dataManager.addResponse(llmResponse);

        // Emit event
        this.eventHandlerService.emitResponseEvent(llmResponse);

        if (this.options.debugMode) {
            console.log(`LLM response tracked: ${modelName}, latency: ${latencyMs}ms`, {
                id,
                requestId,
                agentId,
                responseLength: response.length,
                tokenCount
            });
        }

        return id;
    }

    /**
     * Track an error in language model interaction
     * @param requestId Request ID
     * @param modelName Model name
     * @param error Error object
     * @param agentId Optional agent ID
     * @param metadata Optional metadata
     * @returns Error ID
     */
    trackError(
        requestId: string,
        modelName: string,
        error: Error,
        agentId?: string,
        metadata?: Record<string, any>
    ): string {
        const id = `err-${Date.now()}-${this.nextId++}`;

        const llmError: LLMError = {
            id,
            requestId,
            timestamp: Date.now(),
            modelName,
            error,
            agentId,
            metadata
        };

        this.dataManager.addError(llmError);

        // Emit event
        this.eventHandlerService.emitErrorEvent(llmError);

        console.error(`LLM error tracked: ${modelName}`, {
            id,
            requestId,
            agentId,
            error: error.message
        });

        return id;
    }

    /**
     * Get the corresponding request for a response
     * @param requestId Request ID
     * @returns Request or undefined
     */
    getRequestForResponse(requestId: string): LLMRequest | undefined {
        return this.dataManager.getRequest(requestId);
    }
}
