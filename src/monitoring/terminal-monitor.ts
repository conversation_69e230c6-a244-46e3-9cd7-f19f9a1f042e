/**
 * Terminal Monitor for X10sion
 *
 * This module implements a terminal output monitor that captures output from
 * VS Code terminals, detects patterns, and emits events.
 */

import * as vscode from 'vscode';
import { BufferManagerService, BufferEntry } from './buffer-manager.js';
import { PatternRegistry, TerminalPattern } from './terminal-monitor/pattern-registry.js';
import { TerminalTracker, TerminalInfo } from './terminal-monitor/terminal-tracker.js';
import { OutputProcessor, TerminalMatch } from './terminal-monitor/output-processor.js';

// Re-export types for backward compatibility
export { TerminalPattern } from './terminal-monitor/pattern-registry.js';
export { TerminalMatch } from './terminal-monitor/output-processor.js';
export { TerminalInfo } from './terminal-monitor/terminal-tracker.js';

/**
 * Terminal Monitor Options
 */
export interface TerminalMonitorOptions {
    bufferSize?: number;
    enablePatternMatching?: boolean;
    enableErrorDetection?: boolean;
    enableEventEmission?: boolean;
    debugMode?: boolean;
    maxHistoryItems?: number;
}

/**
 * Terminal Monitor class
 *
 * Monitors terminal output, detects patterns, and emits events.
 */
export class TerminalMonitor implements vscode.Disposable {
    private options: TerminalMonitorOptions;
    private eventEmitter: vscode.EventEmitter<any> = new vscode.EventEmitter<any>();
    private disposables: vscode.Disposable[] = [];
    private statusBarItem: vscode.StatusBarItem;

    // Modular components
    private patternRegistry: PatternRegistry;
    private terminalTracker: TerminalTracker;
    private outputProcessor: OutputProcessor;
    private bufferManagerService: BufferManagerService;

    /**
     * Constructor for TerminalMonitor
     */
    constructor(options: TerminalMonitorOptions = {}) {
        // Set default options
        this.options = {
            bufferSize: 1000,
            enablePatternMatching: true,
            enableErrorDetection: true,
            enableEventEmission: true,
            debugMode: false,
            maxHistoryItems: 1000,
            ...options
        };

        // Initialize modular components
        this.patternRegistry = new PatternRegistry(this.options.debugMode);
        this.terminalTracker = new TerminalTracker(this.eventEmitter, this.options.debugMode);
        this.outputProcessor = new OutputProcessor(this.eventEmitter, {
            enablePatternMatching: this.options.enablePatternMatching !== false,
            enableEventEmission: this.options.enableEventEmission !== false,
            maxHistoryItems: this.options.maxHistoryItems || 1000,
            debugMode: this.options.debugMode || false
        });
        this.bufferManagerService = new BufferManagerService(
            this.options.bufferSize,
            1000, // max line length
            24 * 60 * 60 * 1000 // 24 hours retention
        );

        // Add terminal tracker to disposables
        this.disposables.push(this.terminalTracker);

        // Listen for terminal close events to clear buffers
        this.disposables.push(this.eventEmitter.event((event: any) => {
            if (event.type === 'terminal_untracked') {
                this.bufferManagerService.clearBuffer(event.terminalId);
            }
        }));

        // Create status bar item
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 99);
        this.statusBarItem.text = '$(terminal) Terminal Monitor';
        this.statusBarItem.tooltip = 'Terminal Monitor is active';
        this.statusBarItem.command = 'x10sion.showTerminalMonitorStats';
        this.statusBarItem.show();
        this.disposables.push(this.statusBarItem);

        if (this.options.debugMode) {
            console.log('Terminal Monitor initialized with options:', this.options);
        }
    }







    /**
     * Capture terminal output
     * @param terminal Terminal that produced the output
     * @param data Output data
     */
    captureOutput(terminal: any, data: string): void {
        // Find or track the terminal
        let terminalId = this.terminalTracker.findTerminalId(terminal);

        if (terminalId === undefined) {
            // Terminal not tracked yet, track it
            terminalId = this.terminalTracker.trackTerminal(terminal);
        }

        // Update terminal activity
        this.terminalTracker.updateTerminalActivity(terminalId);

        // Add data to buffer
        this.bufferManagerService.addToBuffer(terminalId, data);

        // Process the output using the output processor
        this.outputProcessor.processOutput(terminalId, data, this.patternRegistry.getAllPatterns());

        // Update status bar based on recent matches
        this.updateStatusBarFromRecentMatches();
    }

    /**
     * Update status bar based on recent matches
     */
    private updateStatusBarFromRecentMatches(): void {
        const recentMatches = this.outputProcessor.getRecentMatches(5);
        if (recentMatches.length > 0) {
            const latestMatch = recentMatches[recentMatches.length - 1];
            this.updateStatusBar(latestMatch.severity);
        }
    }

    /**
     * Update the status bar
     * @param severity Severity of the match
     */
    private updateStatusBar(severity: 'info' | 'warning' | 'error'): void {
        switch (severity) {
            case 'error':
                this.statusBarItem.text = '$(error) Terminal Error';
                this.statusBarItem.tooltip = 'Terminal Monitor detected an error';
                break;
            case 'warning':
                this.statusBarItem.text = '$(warning) Terminal Warning';
                this.statusBarItem.tooltip = 'Terminal Monitor detected a warning';
                break;
            case 'info':
                this.statusBarItem.text = '$(info) Terminal Info';
                this.statusBarItem.tooltip = 'Terminal Monitor detected info';
                break;
        }

        // Reset after a delay
        setTimeout(() => {
            this.statusBarItem.text = '$(terminal) Terminal Monitor';
            this.statusBarItem.tooltip = 'Terminal Monitor is active';
        }, 5000);
    }

    /**
     * Register a pattern
     */
    registerPattern(pattern: TerminalPattern): void {
        this.patternRegistry.registerPattern(pattern);
    }

    /**
     * Unregister a pattern
     */
    unregisterPattern(patternId: string): void {
        this.patternRegistry.unregisterPattern(patternId);
    }

    /**
     * Get a pattern by ID
     */
    getPattern(patternId: string): TerminalPattern | undefined {
        return this.patternRegistry.getPattern(patternId);
    }

    /**
     * Get all patterns
     */
    getAllPatterns(): Map<string, TerminalPattern> {
        return this.patternRegistry.getAllPatterns();
    }

    /**
     * Get matches for a terminal
     */
    getMatchesForTerminal(terminalId: number): TerminalMatch[] {
        return this.outputProcessor.getMatchesForTerminal(terminalId);
    }

    /**
     * Get matches for a pattern
     */
    getMatchesForPattern(patternId: string): TerminalMatch[] {
        return this.outputProcessor.getMatchesForPattern(patternId);
    }

    /**
     * Get all matches
     */
    getAllMatches(): TerminalMatch[] {
        return this.outputProcessor.getAllMatches();
    }

    /**
     * Get terminal info
     */
    getTerminalInfo(terminalId: number): TerminalInfo | undefined {
        return this.terminalTracker.getTerminalInfo(terminalId);
    }

    /**
     * Get all terminal info
     */
    getAllTerminalInfo(): TerminalInfo[] {
        return this.terminalTracker.getAllTerminalInfo();
    }

    /**
     * Get buffer for a terminal
     * @param terminalId Terminal ID
     * @returns Array of buffer entries
     */
    getBuffer(terminalId: number): BufferEntry[] {
        return this.bufferManagerService.getBuffer(terminalId);
    }

    /**
     * Get all buffers
     * @returns Map of terminal IDs to buffers
     */
    getAllBuffers(): Map<number, BufferEntry[]> {
        const buffers = new Map<number, BufferEntry[]>();
        const activeTerminals = this.bufferManagerService.getActiveTerminals();

        for (const terminalId of activeTerminals) {
            buffers.set(terminalId, this.bufferManagerService.getBuffer(terminalId));
        }

        return buffers;
    }

    /**
     * Subscribe to terminal monitor events
     * @param listener Event listener
     * @returns Disposable
     */
    onEvent(listener: (event: any) => void): vscode.Disposable {
        return this.eventEmitter.event(listener);
    }

    /**
     * Get statistics
     */
    getStatistics(): {
        terminals: any;
        patterns: any;
        matches: any;
    } {
        return {
            terminals: this.terminalTracker.getStatistics(),
            patterns: this.patternRegistry.getStatistics(),
            matches: this.outputProcessor.getMatchStatistics()
        };
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        // Clear data
        this.bufferManagerService.clearAllBuffers();
        this.outputProcessor.clearMatches();
        this.patternRegistry.clearPatterns();

        // Dispose of event emitter
        this.eventEmitter.dispose();

        console.log('Terminal Monitor disposed');
    }
}
